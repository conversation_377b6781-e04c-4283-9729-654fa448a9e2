@import url("https://fonts.googleapis.com/css?family=Open+Sans|Roboto:500,700");
html {
  overflow-x: hidden;
}

body {
  background: #fff;
  font-size: 15px;
  font-weight: 400;
  font-family: 'Open Sans', sans-serif;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-font-smoothing: subpixel-antialiased;
  color: #212121;
  line-height: 25px;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5 {
  font-size: 36px;
  font-weight: 700;
  font-family: 'Roboto', sans-serif;
}

a {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}

a:hover {
  text-decoration: none;
}

a a:focus {
  outline: none;
}

p {
  font-weight: 400;
  font-family: 'Open Sans', sans-serif;
  margin: 0px;
  font-size: 14px;
}

ul, ol {
  list-style: outside none none;
  margin: 0;
  padding: 0;
}

ul li, ol li {
  list-style: none;
}

@media(min-width:1024px){
  .nav-alignment{
    display:flex;
    flex-direction:row;
    align-items: center;
  }
}

.hero-heading {
  font-size: 40px;
  font-weight: 700;
  color: #fff;
  text-transform: capitalize;
  line-height: 70px;
  letter-spacing: 0.1rem;
}

.hero-sub-heading {
  font-size: 20px;
  font-weight: 400;
  color: #e6e6e6;
  line-height: 45px;
  letter-spacing: 0.1rem;
}

.section-titile-bg {
  display: inline;
  font-size: 115px;
  font-weight: 700;
  height: 100%;
  left: -173px;
  opacity: 0.1;
  position: absolute;
  top: -14px;
  width: 100%;
  text-align: center;
}

.section-title-header .subtitle {
  font-size: 20px;
  color: #03221B;
  margin-bottom: 10px;
  font-style: italic;
}

.section-title-header p {
  text: center;
  font-weight: 400;
  line-height: 26px;
  padding-bottom: 36px;
}

.section-title {
  font-size: 30px;
  color: #212121;
  line-height: 52px;
  font-weight: 700;
  text-align: center;
  display: block;
  position: relative;
  margin-bottom: 10px;
}

.section-title:before {
  content: "[";
  color: #03221B;
  padding-right: 10px;
}

.section-title:after {
  content: "]";
  color: #03221B;
  padding-left: 10px;
}

.section-subcontent {
  font-size: 16px;
  text: center;
  font-weight: 400;
  line-height: 26px;
  padding-bottom: 36px;
}

.section-sub {
  text-transform: uppercase;
  font-size: 24px;
  line-height: 52px;
  padding-bottom: 15px;
  margin-bottom: 30px;
  position: relative;
}

.section-sub:before {
  position: absolute;
  content: '';
  height: 1px;
  width: 45px;
  left: 50%;
  bottom: 10px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background-color: #03221B;
}

.subtitle {
  font-size: 15px;
  margin-top: 20px;
  font-weight: 500;
  margin-bottom: 10px;
}

.inner-title {
  font-size: 24px;
  font-weight: 700;
  text-tranform: capitalize;
}

.page-tagline {
  font-size: 24px;
  font-weight: 400;
  color: #ddd;
}

.page-title {
  font-size: 62px;
  font-weight: 700;
  color: #fff;
}

.overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
}

.btn {
  font-size: 14px;
  padding: 10px 30px;
  line-height: 22px;
  border-radius: 30px;
  font-family: 'Roboto', sans-serif;
  cursor: pointer;
  font-weight: 500;
  color: #fff;
  text-transform: uppercase;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
  display: inline-block;
}

.btn i {
  margin-right: 5px;
}

.btn:focus,
.btn:active {
  box-shadow: none;
  outline: none;
  color: #fff;
}

.btn-common {
  background-image: linear-gradient(65deg, #258c74 0, #12745d 100%);
  box-shadow: 0 8px 16px rgba(52, 216, 34, 0.25);
  position: relative;
  z-index: 1;
}

.btn-common:hover {
  color: #fff;
  background-image: linear-gradient(65deg, #57c0a8 0, #12745d 100%);
  box-shadow: 0 8px 16px rgba(3, 34, 27, 0.736);
  transition: all .2s ease-in-out;
  -moz-transition: all .2s ease-in-out;
  -webkit-transition: all .2s ease-in-out;
}

.btn-border {
  color: #212121;
  background-color: transparent;
  border: 1px solid rgba(36, 36, 36, 0.2);
}

.btn-border:hover {
  color: #03221B;
  border-color: #03221B;
  background-color: rgba(255, 255, 255, 0.2);
}

.btn-fill {
  background: #fff;
  color: #212121;
}

.btn-fill:hover {
  color: #03221B;
}

.btn-lg {
  padding: 12px 34px;
  text-transform: uppercase;
  font-size: 14px;
}

.btn-rm {
  padding: 7px 10px;
  text-transform: capitalize;
}

button:focus {
  outline: none !important;
}

.icon-close, .icon-check {
  color: #03221B;
}

.bg-drack {
  background: #f1f1f1;
}

.bg-white {
  background: #fff;
}

.mb-30 {
  margin-bottom: 30px;
}

.mt-30 {
  margin-top: 30px;
}

/* ScrollToTop */
a.back-to-top {
  display: none;
  position: fixed;
  bottom: 18px;
  right: 15px;
  text-decoration: none;
}

a.back-to-top i {
  display: block;
  font-size: 22px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: #fff;
  background-image: linear-gradient(65deg, #03221B 0, #075644 100%);
  border-radius: 30px;
  text-align: center;
  transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.14), 0 4px 8px rgba(0, 0, 0, 0.28);
}

a.back-to-top:hover, a.back-to-top:focus {
  text-decoration: none;
}

#preloader {
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999999999;
}

.sk-circle {
  margin: 0px auto;
  width: 40px;
  height: 40px;
  top: 45%;
  position: relative;
}

.sk-circle .sk-child {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.sk-circle .sk-child:before {
  content: '';
  display: block;
  margin: 0 auto;
  width: 15%;
  height: 15%;
  background-color: #03221B;
  border-radius: 100%;
  -webkit-animation: sk-circleBounceDelay 1.2s infinite ease-in-out both;
  animation: sk-circleBounceDelay 1.2s infinite ease-in-out both;
}

.sk-circle .sk-circle2 {
  -webkit-transform: rotate(30deg);
  -ms-transform: rotate(30deg);
  transform: rotate(30deg);
}

.sk-circle .sk-circle3 {
  -webkit-transform: rotate(60deg);
  -ms-transform: rotate(60deg);
  transform: rotate(60deg);
}

.sk-circle .sk-circle4 {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.sk-circle .sk-circle5 {
  -webkit-transform: rotate(120deg);
  -ms-transform: rotate(120deg);
  transform: rotate(120deg);
}

.sk-circle .sk-circle6 {
  -webkit-transform: rotate(150deg);
  -ms-transform: rotate(150deg);
  transform: rotate(150deg);
}

.sk-circle .sk-circle7 {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.sk-circle .sk-circle8 {
  -webkit-transform: rotate(210deg);
  -ms-transform: rotate(210deg);
  transform: rotate(210deg);
}

.sk-circle .sk-circle9 {
  -webkit-transform: rotate(240deg);
  -ms-transform: rotate(240deg);
  transform: rotate(240deg);
}

.sk-circle .sk-circle10 {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.sk-circle .sk-circle11 {
  -webkit-transform: rotate(300deg);
  -ms-transform: rotate(300deg);
  transform: rotate(300deg);
}

.sk-circle .sk-circle12 {
  -webkit-transform: rotate(330deg);
  -ms-transform: rotate(330deg);
  transform: rotate(330deg);
}

.sk-circle .sk-circle2:before {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}

.sk-circle .sk-circle3:before {
  -webkit-animation-delay: -1s;
  animation-delay: -1s;
}

.sk-circle .sk-circle4:before {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}

.sk-circle .sk-circle5:before {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}

.sk-circle .sk-circle6:before {
  -webkit-animation-delay: -0.7s;
  animation-delay: -0.7s;
}

.sk-circle .sk-circle7:before {
  -webkit-animation-delay: -0.6s;
  animation-delay: -0.6s;
}

.sk-circle .sk-circle8:before {
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}

.sk-circle .sk-circle9:before {
  -webkit-animation-delay: -0.4s;
  animation-delay: -0.4s;
}

.sk-circle .sk-circle10:before {
  -webkit-animation-delay: -0.3s;
  animation-delay: -0.3s;
}

.sk-circle .sk-circle11:before {
  -webkit-animation-delay: -0.2s;
  animation-delay: -0.2s;
}

.sk-circle .sk-circle12:before {
  -webkit-animation-delay: -0.1s;
  animation-delay: -0.1s;
}

@-webkit-keyframes sk-circleBounceDelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes sk-circleBounceDelay {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.section-padding {
  padding: 60px 0;
}

.no-padding {
  padding: 0 !important;
}

.padding-left-none {
  padding-left: 0;
}

.padding-right-none {
  padding-right: 0;
}

#page-banner-area {
  background: url(../img/background/banner.jpg) no-repeat;
  background-position: 50% 50%;
  background-size: cover;
}

.page-banner {
  position: relative;
  min-height: 250px;
  color: #fff;
}

.page-banner:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}

.page-banner .page-banner-title {
  position: absolute;
  top: 50%;
  width: 100%;
  height: 100%;
  text-transform: uppercase;
}

.page-banner .page-banner-title h2 {
  color: #fff;
  font-size: 40px;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

#white-bg {
  background: #fff;
}

.navbar-brand {
  position: relative;
  padding: 0px;
}

.navbar-brand img {
  width: 75%;
}

.top-nav-collapse {
  background: #fff;
  z-index: 999999;
  top: 0px !important;
  min-height: 58px;
  box-shadow: 0px 3px 6px 3px rgba(0, 0, 0, 0.06);
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  background: #fff !important;
}

.top-nav-collapse .navbar-brand {
  top: 0px;
}

.top-nav-collapse .navbar-nav .nav-link {
  color: #0f5f4c !important;
}

.top-nav-collapse .navbar-nav .nav-link:hover {
  color: #4ce3c0 !important;
}

.top-nav-collapse .navbar-nav li.active a.nav-link {
  color: #4ce3c0 !important;
}

.navbar-expand-lg .navbar-toggler {
  background: transparent;
  border: 1px solid #03221B;
  background-image: linear-gradient(65deg, #03221B 0, #03221B 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
  cursor: pointer;
}

.indigo {
  background: transparent;
}

.navbar-expand-lg .navbar-nav .nav-link {
  color: #fff;
  font-size: 14px;
  padding: 0 15px;
  margin-top: 10px;
  margin-bottom: 10px;
  font-family: 'Roboto', sans-serif;
  font-weight: 500;
  line-height: 40px;
  text-transform: uppercase;
  background: transparent;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  position: relative;
}

.navbar-expand-lg .navbar-nav li > a:before {
  content: '';
  position: absolute;
  top: 19px;
  left: 5%;
  margin-left: -10px;
  width: 15px;
  height: 2px;
  background: #03221B;
  -webkit-transform: scale3d(0, 1, 1);
  -moz-transform: scale3d(0, 1, 1);
  transform: scale3d(0, 1, 1);
  -webkit-transition: -webkit-transform 0.1s;
  -moz-transition: -webkit-transform 0.1s;
  transition: transform 0.1s;
}

.navbar-expand-lg .navbar-nav .active a:before {
  -webkit-transform: scale3d(1, 1, 1);
  -moz-transform: scale3d(1, 1, 1);
  transform: scale3d(1, 1, 1);
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 0.3s;
  -moz-transition-duration: 0.3s;
  transition-duration: 0.3s;
}

.navbar-expand-lg .navbar-nav li a:hover,
.navbar-expand-lg .navbar-nav li .active > a,
.navbar-expand-lg .navbar-nav li a:focus {
  background-image: linear-gradient(65deg,  #5ad9bc 0, #0b735b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  outline: none;
}

.navbar-expand-lg .navbar-nav .nav-link:focus,
.navbar-expand-lg .navbar-nav .nav-link:hover {
  background-image: linear-gradient(65deg, #5ad9bc 0, #0b735b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text !important;
}

.navbar {
  padding: 0;
}

.navbar li.active a.nav-link {
  background-image: linear-gradient(65deg, #5ad9bc 0, #0b735b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text !important;
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-menu {
  margin: 0;
  padding: 0;
  display: none;
  position: absolute;
  z-index: 99;
  min-width: 210px;
  background-color: #fff;
  white-space: nowrap;
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  animation: fadeIn 0.4s;
  -webkit-animation: fadeIn 0.4s;
  -moz-animation: fadeIn 0.4s;
  -o-animation: fadeIn 0.4s;
  -ms-animation: fadeIn 0.4s;
}

.dropdown-menu:before {
  content: "";
  display: inline-block;
  position: absolute;
  bottom: 100%;
  left: 20%;
  margin-left: -5px;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-bottom: 10px solid #f5f5f5;
}

.dropdown:hover .dropdown-menu {
  display: block;
  position: absolute;
  text-align: left;
  top: 100%;
  border: none;
  animation: fadeIn 0.4s;
  -webkit-animation: fadeIn 0.4s;
  -moz-animation: fadeIn 0.4s;
  -o-animation: fadeIn 0.4s;
  -ms-animation: fadeIn 0.4s;
  background: #f5f5f5;
}

.dropdown .dropdown-menu .dropdown-item {
  width: 100%;
  padding: 12px 20px;
  font-size: 14px;
  color: #212121;
  border-bottom: 1px solid #f1f1f1;
  text-decoration: none;
  display: inline-block;
  float: left;
  clear: both;
  position: relative;
  outline: 0;
  transition: all 0.3s ease-in-out;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
}

.dropdown .dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.dropdown .dropdown-menu .dropdown-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.dropdown .dropdown-item:focus,
.dropdown .dropdown-item:hover,
.dropdown .dropdown-item.active {
  color: #4ce3c0;
}

.dropdown-item.active, .dropdown-item:active {
  background: transparent;
}

.fadeInUpMenu {
  -webkit-animation-name: fadeInUpMenu;
  animation-name: fadeInUpMenu;
}

/* only small tablets */
@media (min-width: 768px) and (max-width: 991px) {
  .bg-inverse {
    background: #fff !important;
    box-shadow: 0px 3px 6px 3px rgba(0, 0, 0, 0.06);
  }
  .navbar-expand-lg .navbar-brand, .navbar-expand-lg .navbar-toggler {
    margin: 0px 15px;
  }
  .navbar-nav {
    padding: 5px 15px;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    margin-top: 0;
    margin-bottom: 0;
    padding: 0px 40px;
    color: #212121;
  }
}

@media (max-width: 991px) {
  .bg-inverse {
    background: #fff !important;
    box-shadow: 0px 3px 6px 3px rgba(0, 0, 0, 0.06);
  }
  .navbar-expand-lg .navbar-brand, .navbar-expand-lg .navbar-toggler {
    margin: 0px 15px;
  }
  .navbar-nav {
    padding: 5px 15px;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    margin-top: 0;
    margin-bottom: 0;
    padding: 0px 40px;
    color: #212121;
  }
}

#about {
  background: #F6F9F9;
}

.about-content {
  height: 100%;
  display: table;
}

.about-content > div {
  vertical-align: middle;
  display: table-cell;
}

.about-content .about-text h2 {
  font-size: 40px;
  font-weight: 700;
}

.about-content .about-text h6 {
  font-size: 18px;
  line-height: 32px;
  font-weight: 400;
  margin-top: 20px;
}

.about-content .about-text p {
  margin-top: 20px;
  margin-bottom: 20px;
}

.about-content ul {
  margin-bottom: -8px;
}

.about-content ul li {
  list-style: none;
  line-height: 30px;
  position: relative;
  padding-left: 25px;
}

.about-content ul li i {
  position: absolute;
  left: 0;
  top: 7px;
  font-size: 16px;
  color: #03221B;
}

.about-content .btn-about {
  margin-top: 30px;
}

.about-content .btn-about .btn {
  margin-right: 10px;
}

#event-up .event-item {
  position: relative;
}

#event-up .event-item .overlay-text {
  text-align: center;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

#event-up .event-item .content {
  position: absolute;
  top: 30%;
  width: 100%;
  color: #fff;
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

#event-up .event-item .content h3 {
  font-size: 16px;
  text-transform: uppercase;
}

#event-up .event-item .content a {
  color: #03221B;
  margin-top: 10px;
}

#event-up .event-item:hover .content {
  top: 50%;
}

#event-up .event-item .overlay-text:hover {
  opacity: 1;
}

/* ==========================================================================
   Video Section Style
   ========================================================================== */
.ready-to-play {
  height: 560px;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.ready-to-play:before {
  content: '';
  background-image: linear-gradient(0deg, #03221B 0, #085d49 100%);
  position: absolute;
  display: block;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.7;
}

video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -100;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  background-size: cover;
  transition: 1s opacity;
}

.stop {
  opacity: 1;
}

.stopfade .ready-to-play:before {
  opacity: 1;
}

h1 {
  font-size: 3rem;
  text-transform: uppercase;
  margin-top: 0;
  letter-spacing: .3rem;
}

.video-text {
  background-size: cover;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 999;
  color: #fff;
}

.video-text {
  text-align: center;
}

.video-text h4 {
  margin-bottom: 15px;
  color: #fff;
  text-transform: uppercase;
}

.video-text h1 {
  margin-bottom: 25px;
  color: #fff;
}

.video-text p {
  color: #fff;
}

.tb-t {
  display: table;
  height: 100%;
  margin: auto;
}

.tb-t .tb-c {
  display: table-cell;
  vertical-align: middle;
}

.tb-t .tb-c button {
  background: transparent;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 80px;
  height: 80px;
}

.tb-t .tb-c button i {
  font-size: 30px;
  color: #fff;
  line-height: 70px;
}

.tb-t .tb-c button:focus {
  outline: none;
}

.counter-section {
  background: url(../../img/img-1.webp) no-repeat center;
  background-size: auto auto;
  padding: 80px 0;
  background-size: cover;
  background-attachment: relative;
  position: relative;
}

.counter-section:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
}

.counter-section .counter {
  position: relative;
  background: rgba(255, 255, 255, 0.3);
  padding: 35px 0;
  border-radius: 4px;
  padding-left: 45px;
  margin-left: 20px;
}

.counter-section .counter .icon {
  background-image: linear-gradient(0deg, #03221B 0, #064a3a 100%);
  color: #fff;
  text-align: center;
  line-height: 50px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  position: absolute;
  top: 47%;
  left: -30px;
  margin-top: -25px;
}

.counter-section .counter .icon i {
  font-size: 24px;
  line-height: 60px;
  color: #fff;
}

.counter-section .counter p {
  font-size: 15px;
  color: #fff;
  font-weight: 400;
}

.counter-section .counter .counterUp {
  color: #fff;
  font-size: 50px;
  margin-bottom: 20px;
  font-weight: 700;
}

/* Sponsors Area */
#sponsors {
  background: url(../../img/img-1.webp) no-repeat center;
  background-attachment: scroll;
  background-size: auto auto;
  background-size: cover;
  background-attachment: fixed;
  position: relative;
}

#sponsors .section-title-header .section-title {
  color: #fff;
}

#sponsors .section-title-header p {
  color: #fff;
}

#sponsors .sponsors-logo a img {
  margin: 30px;
}

#sponsors .spnsors-logo:hover {
  border-color: transparent;
  box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.2);
}

/* Schedule Area */
#schedules {
  background: #f3f4f3;
}

#schedule-tab {
  position: relative;
}

.schedule .nav-tabs {
  display: block;
  border-bottom: none;
}

.schedule .nav-tabs .nav-item {
  margin-bottom: 16px;
  text-align: center;
  margin: 0 10px;
  display: inline-block;
}

.schedule .nav-tabs .nav-link {
  padding: 12px 15px;
  background: #fff;
  padding-top: 16px;
  border-radius: 4px;
  box-shadow: 0 8px 24px #E2E8ED;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}

.schedule .nav-tabs .nav-item.show .nav-link, .schedule .nav-tabs .nav-link.active,
.schedule .nav-tabs .nav-item .nav-link:hover {
  border-color: transparent;
  position: relative;
  background-image: linear-gradient(65deg, #03221B 0, #08624d 100%);
}

.schedule .nav-tabs .nav-item.show .nav-link h4, .schedule .nav-tabs .nav-link.active h4,
.schedule .nav-tabs .nav-item .nav-link:hover h4 {
  color: #fff;
}

.schedule .nav-tabs .nav-item.show .nav-link h5, .schedule .nav-tabs .nav-link.active h5,
.schedule .nav-tabs .nav-item .nav-link:hover h5 {
  color: #fff;
}

.schedule .nav-tabs .nav-item.show .nav-link:before, .schedule .nav-tabs .nav-link.active:before {
  content: '';
  position: absolute;
  left: 42%;
  bottom: -24px;
  border-width: 12px;
  border-style: solid;
  border-color: #03221B transparent transparent transparent;
}

.schedule .item-text {
  border-radius: 4px;
  position: relative;
}

.schedule .item-text h4 {
  font-size: 15px;
  color: #2f323c;
  line-height: 22px;
  font-weight: 400;
  text-transform: uppercase;
}

.schedule .item-text h5 {
  font-size: 14px;
  color: #848484;
  line-height: 16px;
  font-weight: 400;
  text-transform: capitalize;
}

.schedule .schedule-tab-content .tab-content {
  box-shadow: 0 8px 24px #E2E8ED;
}

.schedule .schedule-tab-content .card {
  border: none;
}

.schedule .show .card-body {
  background: #f3f4f3;
}

.schedule .card .schedule-slot-time {
  background-image: linear-gradient(65deg, #03221B 0, #085140 100%);
  color: #fff;
  padding: 38px 28px;
  float: left;
  font-size: 18px;
  font-weight: 700;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 18%;
  flex: 0 0 18%;
  max-width: 18%;
}

.schedule .card-header {
  position: relative;
  padding: 35px 40px 35px 224px;
  border-bottom: 1px dashed #e5e5e5;
  cursor: pointer;
  border-left: none;
  width: 100%;
}

.schedule .card-header .images-box {
  width: 70px;
  height: 70px;
  float: left;
  margin-right: 20px;
}

.schedule .card-header .images-box img {
  border-radius: 50%;
}

.schedule .card-header .time {
  color: #777777;
  font-size: 13px;
  margin-bottom: 5px;
}

.schedule .card-header h4 {
  font-weight: 700;
  font-size: 18px;
  color: #212121;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.schedule .card-header .name {
  position: relative;
  font-size: 14px;
  color: #777777;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 5px;
}

.schedule .card-body {
  padding: 30px 45px 30px 45px;
  background: #fff;
}

.schedule .card-body .location {
  margin-top: 8px;
  font-size: 13px;
  color: #777777;
}

.schedule .card-body .location span {
  color: #03221B;
  margin-right: 5px;
}

.accordion .card {
  border-radius: 0px;
  margin-bottom: -2px;
}

.accordion .header-title {
  position: relative;
}

.accordion .header-title:after {
  font-family: "LineIcons";
  content: "\e93a";
  position: absolute;
  right: 10px;
  color: #999;
  top: 50%;
  line-height: 1;
  padding: 0px 5px;
  margin-top: -7px;
}

.accordion .collapsed:after {
  content: "\e940";
}

.accordion .card-header {
  background: transparent;
  cursor: pointer;
  padding: 0;
}

.accordion .card-header i {
  color: #03221B;
  width: 40px;
  height: 40px;
  display: inline-block;
  line-height: 40px;
  text-align: center;
  margin-right: 10px;
  border-right: 1px solid #ddd;
}

.accordion .card-body {
  padding-left: 56px;
}

/* Inforamation Bar */
#information-bar {
  background: #fff;
  position: relative;
}

#information-bar .inforation-wrapper {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 8px 24px #E2E8ED;
  margin-bottom: -35px;
  margin-top: -35px;
  padding: 45px 0;
  position: relative;
  top: -40px;
  z-index: 999;
}

#information-bar ul {
  text-align: center;
}

#information-bar ul li {
  display: inline-block;
}

#information-bar ul li i {
  text-align: center;
  padding: 0 12px;
  font-size: 32px;
  vertical-align: middle;
  background-image: linear-gradient(0deg, #03221B 0, #0a5a48 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#information-bar ul li span {
  font-size: 13px;
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

#information-bar ul li span b {
  font-size: 20px;
  color: #374045;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

/* Services Item */
.intro {
  background: #fff;
}

.single-intro-text {
  padding: 45px 50px 40px 60px;
  -webkit-box-shadow: 0 8px 24px #E2E8ED;
  box-shadow: 0 8px 24px #E2E8ED;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -ms-border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin: 15px 0;
  background: #fff;
  z-index: 2;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.single-intro-text:hover {
  box-shadow: 0 16px 48px #E2E8ED;
}

.single-intro-text i {
  position: absolute;
  left: -10px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  top: 20px;
  font-size: 55px;
  color: #bababa;
}

.single-intro-text h3 {
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: 700;
}

.single-intro-text p {
  margin-bottom: 0;
}

.single-intro-text .count-number {
  position: absolute;
  right: -55px;
  bottom: -55px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  background-image: linear-gradient(0deg, #03221B 0, #12745d 100%);
  color: #fff;
  display: block;
  text-align: left;
  font-size: 16px;
  font-weight: 700;
  padding: 25px 30px;
}

.single-intro-text:hover i {
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Gallery Section */
#gallery {
  background: #f3f4f3;
  position: relative;
}

#gallery .gallery-box {
  position: relative;
  margin-bottom: 30px;
  padding: 10px;
  box-shadow: 0 8px 24px #E2E8ED;
}

#gallery .gallery-box .overlay-box {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  padding-top: 35%;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

#gallery .gallery-box .overlay-box i {
  background: #ffffff;
  color: #03221B;
  font-size: 30px;
  width: 50px;
  height: 50px;
  display: inline-block;
  line-height: 50px;
  border-radius: 50%;
  transform: scale(0, 0);
  -webkit-transform: scale(0, 0);
  -moz-transform: scale(0, 0);
  -ms-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

#gallery .gallery-box:hover .overlay-box {
  background: #03221B;
  transform: scale(1, 1);
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
  transition: all 500ms ease;
}

#gallery .gallery-box:hover .overlay-box i {
  -webkit-transform: scale(1, 1);
  -moz-transform: scale(1, 1);
  -ms-transform: scale(1, 1);
  -o-transform: scale(1, 1);
}

/* Team Item */
.team-item:hover {
  box-shadow: 0 16px 48px #E2E8ED;
}

.team-item {
  margin: 15px 0;
  padding: 15px;
  border-radius: 4px;
  position: relative;
  box-shadow: 0 8px 24px #E2E8ED;
  transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
}

.team-item .team-img {
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.team-item .team-overlay {
  text-align: center;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.team-item .overlay-social-icon {
  position: absolute;
  top: 50%;
  width: 100%;
  color: #fff;
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.team-item .overlay-social-icon .social-icons {
  padding-left: 0;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  -webkit-transform: translateY(-30px);
  -moz-transform: translateY(-30px);
  transform: translateY(-30px);
  transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
}

.team-item .overlay-social-icon .social-icons li {
  float: left;
}

.team-item .overlay-social-icon .social-icons li a {
  letter-spacing: 0px;
  outline: 0 !important;
}

.team-item .overlay-social-icon .social-icons li a i {
  font-size: 20px;
  color: #fff;
  width: 60px;
  height: 60px;
  background: #03221B;
  display: block;
  border-radius: 0px;
  line-height: 60px;
  transition: all 0.3s ease-in-out 0s;
  -moz-transition: all 0.3s ease-in-out 0s;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
}

.team-item .overlay-social-icon .social-icons li .lni-facebook-filled {
  background: rgba(59, 89, 152, 0.7);
}

.team-item .overlay-social-icon .social-icons li .lni-facebook-filled:hover {
  background: #3B5998;
}

.team-item .overlay-social-icon .social-icons li .lni-twitter-filled {
  background: rgba(29, 161, 242, 0.7);
}

.team-item .overlay-social-icon .social-icons li .lni-twitter-filled:hover {
  background: #1da1f2;
}

.team-item .overlay-social-icon .social-icons li .lni-google-plus {
  background: #03221B;
}

.team-item .overlay-social-icon .social-icons li .lni-google-plus:hover {
  background: #03221B;
}

.team-item .overlay-social-icon .social-icons li .lni-pinterest {
  background: #03221B;
}

.team-item .overlay-social-icon .social-icons li .lni-pinterest:hover {
  background: #03221B;
}

.team-item .info-text {
  padding: 20px 0px 5px;
}

.team-item .info-text h3 {
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 700;
  margin-bottom: 5px;
}

.team-item .info-text h3 a {
  color: #212121;
}

.team-item .info-text h3 a:hover {
  color: #03221B;
}

.team-item .info-text p {
  margin: 0;
  color: #888;
}

.team-item:hover .team-overlay {
  opacity: 1;
}

.team-item:hover .social-icons {
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  transform: translateY(0px);
}

.blog-item {
  background-color: #fff;
  border-radius: 7px;
  margin-bottom: 30px;
  text-align: center;
  box-shadow: 0 8px 24px #E2E8ED;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.blog-item .blog-image {
  width: 100%;
  max-width: 370px;
  position: relative;
  overflow: hidden;
}

.blog-item .blog-image img {
  background: #000;
  border-top-left-radius: 7px;
  border-top-right-radius: 7px;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.blog-item .icon {
  position: absolute;
  top: -25px;
  left: 42%;
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  color: #ffffff;
  display: block;
  font-size: 26px;
  box-shadow: 0 16px 48px #E2E8ED;
  width: 60px;
  height: 60px;
  line-height: 65px;
  border-radius: 30px;
}

.blog-item .descr {
  padding: 50px 25px 20px;
  color: #212121;
  position: relative;
}

.blog-item .descr h3 {
  line-height: 22px;
  margin-bottom: 15px;
}

.blog-item .descr h3 a {
  font-weight: 400;
  text-align: center;
  font-size: 20px;
  color: #212121;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.blog-item .descr h3 a:hover {
  color: #03221B;
}

.blog-item .meta-tags {
  background: #f5f6f6;
  padding: 10px;
}

.blog-item .meta-tags span {
  font-size: 14px;
  color: #999;
  margin: 15px;
}

.blog-item .meta-tags span i {
  margin-right: 5px;
  color: #03221B;
}

.blog-item .meta-tags span a {
  color: #999;
}

.blog-item .meta-tags span a:hover {
  color: #03221B;
}

.blog-item:hover {
  box-shadow: 0 16px 48px #E2E8ED;
}

.blog-item:hover .blog-image img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

/* Conact Home Page Section */
.contact {
  background: #111111;
}

#conatiner-map {
  width: 100%;
  height: 580px;
}

.title-forme {
  font-size: 28px;
  margin-bottom: 20px;
}

.contact-item {
  position: relative;
  text-align: center;
}

.contact-item i {
  width: 70px;
  height: 70px;
  display: inline-block;
  line-height: 70px;
  font-size: 24px;
  text-align: center;
  margin-bottom: 30px;
  background: #2d2d2d;
  color: #fff;
  border-radius: 4px;
  -webkit-transition: .3s all;
  -o-transition: .3s all;
  transition: .3s all;
}

.contact-item .contact-info h3 {
  color: #03221B;
  font-size: 18px;
  margin-bottom: 10px;
}

.contact-item .contact-info p {
  color: #fff;
}

/* Contact Forme */
#google-map-area {
  position: relative;
}

#google-map-area .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.container-form {
  left: 0%;
  padding: 40px 15px;
  margin-left: -15px;
  z-index: 99;
}

.container-form .form-wrapper {
  background-color: white;
  padding: 56px;
  box-shadow: 0 8px 24px #E2E8ED;
}

.container-form .form-wrapper .form-group {
  margin-bottom: 30px;
}

.container-form .form-wrapper .form-control {
  display: block;
  width: 100%;
  line-height: 26px;
  font-size: 14px;
  box-shadow: none;
  color: #848484;
  background: #f7f7f7;
  border: 1px solid transparent;
  padding: 7px 19px;
  border-radius: 30px;
  background-image: none;
  background-clip: padding-box;
}

.container-form .form-wrapper .form-control:focus {
  color: #212121;
  background-color: #fff;
  border-color: #03221B;
  box-shadow: none;
  outline: 0;
}

.container-form textarea {
  border-radius: 4px !important;
}

.form-submit .btn-common {
  width: 100%;
}

.list-unstyled li {
  margin-top: 10px;
  color: #03221B !important;
}

.text-danger {
  margin-top: 20px;
  font-size: 18px;
}

.form-control {
  display: block;
  width: 100%;
  line-height: 27px;
  font-size: 14px;
  box-shadow: none;
  color: #848484;
  background: #f7f7f7;
  padding: 7px 19px;
  border: 1px solid #e5e5e5;
  border-radius: 0px;
  transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -ms-transition: all 500ms ease;
  -o-transition: all 500ms ease;
}

.form-control:focus {
  border-color: #03221B;
  box-shadow: none;
  outline: 0;
}

/* Inforamation Bar */
#contact-text {
  background: #161E26;
}

#contact-text .contact-wrapper {
  background: #fff;
  margin-bottom: -35px;
  margin-top: -20px;
  padding: 30px 0;
  border-radius: 4px;
  position: relative;
  top: -40px;
  z-index: 999;
}

#contact-text ul {
  text-align: center;
}

#contact-text ul li {
  display: inline-block;
}

#contact-text ul li i {
  text-align: center;
  padding: 0 12px;
  font-size: 28px;
  vertical-align: middle;
  color: #03221B;
}

#contact-text ul li span {
  font-size: 14px;
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

#contact-text ul li span b {
  font-size: 20px;
  color: #374045;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

/* Footer Area Start */
footer {
  background: #161E26;
  text-align: center;
}

.site-info {
  position: relative;
  padding: 30px 0;
  margin-bottom: 30px;
}

.site-info p {
  font-size: 13px;
  color: #9e9e9e;
}

.site-info a {
  color: #fff;
}

.site-info a:hover {
  color: #03221B;
}

.social-icons-footer ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.social-icons-footer ul li {
  display: inline-block;
}

.social-icons-footer a {
  color: #888;
  display: inline-block;
  margin-left: 5px;
  margin-right: 5px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid rgba(255, 254, 254, 0.07);
  line-height: 40px;
  width: 40px;
  height: 40px;
  text-align: center;
  font-size: 16px;
}

.social-icons-footer .facebook a:hover {
  border-color: #4867aa;
  color: #4867aa;
}

.social-icons-footer .twitter a:hover {
  border-color: #1da1f2;
  color: #1da1f2;
}

.social-icons-footer .linkedin a:hover {
  border-color: #0077B5;
  color: #0077B5;
}

.social-icons-footer .google a:hover {
  border-color: #03221B;
  color: #03221B;
}

.social-icons-footer .pinterest a:hover {
  border-color: #03221B;
  color: #03221B;
}

.link-list {
  margin: 15px;
}

.link-list li {
  display: inline-block;
  padding: 7px 10px;
}

.link-list li a {
  color: #9e9e9e;
}

.link-list li a:hover {
  color: #03221B;
}

/* Subscribe Form */
.subscribe-inner {
  padding: 50px 0;
  text-align: center;
}

.subscribe-inner .subscribe-title {
  color: #fff;
  font-size: 26px;
  text-center: center;
  margin-bottom: 50px;
  position: relative;
}

.subscribe-inner p {
  color: #888;
  margin-bottom: 30px;
}

.subscribe-inner .sub-btn {
  margin: 0;
  width: 12rem;
  padding: 18px 40px;
  border-radius: 30px;
  min-width: 12rem;
  position: absolute;
  top: 0;
  border: none;
  bottom: 0;
  right: 0;
}

.subscribe-inner .sub-btn i {
  margin-right: 5px;
}

.subscribe-inner .form-control {
  position: relative;
  background: #252D37;
  border: 1px solid #252D37;
  border-radius: 0px;
  box-shadow: none;
  color: #fff !important;
  border-radius: 30px;
  height: 55px;
  margin-right: -1px;
  width: 90%;
}

.subscribe-inner .form-control:focus {
  color: #212121;
  border-color: #03221B;
  outline: 0;
}

.subscribe-inner .form-inline {
  position: relative;
  margin-bottom: 30px;
  margin: 0 auto;
  width: 100%;
}

.footer-logo {
  margin-bottom: 50px;
}

/* ==========================================================================
   Countdown Style Start
   ========================================================================== */
#count {
  background: #F6F9F9;
}

#count .count-wrapper {
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  border-radius: 4px;
  box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.08);
  margin-bottom: -35px;
  margin-top: -35px;
  padding: 15px 0;
  position: relative;
  top: -40px;
  z-index: 999;
  display: inline-block;
  width: 100%;
}

.time-count div {
  text-align: center;
}

.time-entry {
  display: inline-block;
  margin: 15px 0;
  width: 24%;
  float: left;
  margin-right: 0;
  border: none;
  position: relative;
  height: auto;
  font-family: 'Roboto', sans-serif;
  border-radius: 4px;
  text-align: center;
  font-weight: 400;
  color: #fff;
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 22px;
}

.time-entry span {
  font-size: 60px;
  line-height: 45px;
  font-weight: 700;
  letter-spacing: 0.3;
  font-family: 'Roboto', sans-serif;
  display: block;
  color: #fff;
  margin-bottom: 10px;
}

.time-entry b {
  position: absolute;
  right: 0;
  top: 50%;
  bottom: 0;
  margin: auto;
  font-size: 30px;
  -webkit-transform: translateY(-18%);
  -ms-transform: translateY(-18%);
  transform: translateY(-18%);
}

.time-entry:first-child {
  border-left: none;
}

/* Pricing Ticket */
#pricing {
  background: #f8fafa;
  position: relative;
}

#pricing .price-block-wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;
  box-shadow: 0 8px 24px #E2E8ED;
  border-radius: 4px;
  background: #fff;
  padding: 50px 40px;
  -webkit-transition: all 300ms ease-in-out;
  -moz-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

#pricing .price-block-wrapper .icon {
  padding: 20px;
}

#pricing .price-block-wrapper .icon i {
  font-size: 60px;
  color: #03221B;
}

#pricing .price-block-wrapper .colmun-title h5 {
  font-size: 20px;
  color: #212121;
  margin-bottom: 20px;
  text-transform: uppercase;
  font-weight: 700;
}

#pricing .price-block-wrapper .price {
  padding-top: 10px;
  transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
}

#pricing .price-block-wrapper .price h2 {
  font-size: 38px;
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 42px;
  margin-bottom: 7px;
}

#pricing .price-block-wrapper .price span {
  text-transform: uppercase;
  font-size: 13px;
}

#pricing .price-block-wrapper .price p {
  font-size: 15px;
  color: #5c5c5c;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 30px;
  text-transform: uppercase;
  transition: all 500ms ease;
  -webkit-transition: all 500ms ease;
  -moz-transition: all 500ms ease;
}

#pricing .price-block-wrapper .pricing-list {
  padding: 15px 0;
}

#pricing .price-block-wrapper .pricing-list ul {
  padding-bottom: 15px;
}

#pricing .price-block-wrapper .pricing-list ul li {
  position: relative;
  line-height: 40px;
  font-size: 15px;
  font-weight: 400;
  padding: 2px 0px;
}

#pricing .price-block-wrapper .pricing-list ul li i {
  margin-right: 7px;
  font-size: 12px;
  color: #03221B;
}

#pricing .price-block-wrapper .btn i {
  font-size: 12px;
  margin-right: 5px;
}

#pricing .price-block-wrapper:hover {
  box-shadow: 0 16px 48px #E2E8ED;
}

#pricing .active {
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  box-shadow: 0 16px 48px #E2E8ED;
}

#pricing .active .icon i {
  color: #fff;
}

#pricing .active .colmun-title h5 {
  color: #fff;
}

#pricing .active .price h2 {
  background-image: linear-gradient(120deg, #fff 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#pricing .active .price span {
  color: #fff;
}

#pricing .active .price p {
  color: #fff;
}

#pricing .active .pricing-list ul li {
  color: #fff;
}

#pricing .active .pricing-list ul li i {
  color: #fff;
}

#pricing .active .btn {
  background: #fff;
  color: #212121;
}

/* ==========================================================================
3. Hero Area
========================================================================== */
.hero-area-bg {
  background: url(../img/hero-area.webp) no-repeat;
  background-size: cover;
}

#hero-area {
  color: #fff;
  overflow: hidden;
  position: relative;
}

#hero-area .overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background: rgba(0, 0, 0, 0.7);
}

#hero-area .contents {
  padding: 160px 20px 180px;
}

#hero-area .contents .icon {
  margin-bottom: 30px;
}

#hero-area .contents .icon i {
  background-image: linear-gradient(65deg, #03221B 0, #12745d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 50px;
}

#hero-area .contents .banner-info {
  font-size: 18px;
  font-style: italic;
  margin-bottom: 22px;
  line-height: 24px;
  color: #fff;
}

#hero-area .contents .head-title {
  color: #fff;
  font-size: 48px;
  line-height: 48px;
  text-transform: uppercase;
  font-weight: 700;
  margin-bottom: 22px;
}

#hero-area .contents .banner-desc {
  font-size: 17px;
  font-weight: 400;
  color: #fff;
  line-height: 30px;
  margin-bottom: 30px;
  padding: 0px 70px;
}

#hero-area .contents .btn {
  margin: 0px 10px 0px;
  font-size: 16px;
  padding: 12px 40px;
  text-transform: uppercase;
}
