(()=>{var R=({areGroupsCollapsedByDefault:o,canTrackDeselectedRecords:h,currentSelectionLivewireProperty:r,maxSelectableRecords:s,selectsCurrentPageOnly:l,$wire:d})=>({checkboxClickController:null,groupVisibility:[],isLoading:!1,selectedRecords:new Set,deselectedRecords:new Set,isTrackingDeselectedRecords:!1,shouldCheckUniqueSelection:!0,lastCheckedRecord:null,livewireId:null,entangledSelectedRecords:r?d.$entangle(r):null,init(){this.livewireId=this.$root.closest("[wire\\:id]")?.attributes["wire:id"].value,d.$on("deselectAllTableRecords",()=>this.deselectAllRecords()),d.$on("scrollToTopOfTable",()=>this.$root.scrollIntoView({block:"start",inline:"nearest"})),r&&(s!==1?this.selectedRecords=new Set(this.entangledSelectedRecords):this.selectedRecords=new Set(this.entangledSelectedRecords?[this.entangledSelectedRecords]:[])),this.$nextTick(()=>this.watchForCheckboxClicks()),Livewire.hook("element.init",({component:e})=>{e.id===this.livewireId&&this.watchForCheckboxClicks()})},mountAction(...e){d.set("isTrackingDeselectedTableRecords",this.isTrackingDeselectedRecords,!1),d.set("selectedTableRecords",[...this.selectedRecords],!1),d.set("deselectedTableRecords",[...this.deselectedRecords],!1),d.mountAction(...e)},toggleSelectRecordsOnPage(){let e=this.getRecordsOnPage();if(this.areRecordsSelected(e)){this.deselectRecords(e);return}this.selectRecords(e)},toggleSelectRecords(e){this.areRecordsSelected(e)?this.deselectRecords(e):this.selectRecords(e)},getSelectedRecordsCount(){return this.isTrackingDeselectedRecords?(this.$refs.allSelectableRecordsCount?.value??this.deselectedRecords.size)-this.deselectedRecords.size:this.selectedRecords.size},getRecordsOnPage(){let e=[];for(let t of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])e.push(t.value);return e},selectRecords(e){s===1&&(this.deselectAllRecords(),e=e.slice(0,1));for(let t of e)if(!this.isRecordSelected(t)){if(this.isTrackingDeselectedRecords){this.deselectedRecords.delete(t);continue}this.selectedRecords.add(t)}this.updatedSelectedRecords()},deselectRecords(e){for(let t of e){if(this.isTrackingDeselectedRecords){this.deselectedRecords.add(t);continue}this.selectedRecords.delete(t)}this.updatedSelectedRecords()},updatedSelectedRecords(){if(s!==1){this.entangledSelectedRecords=[...this.selectedRecords];return}this.entangledSelectedRecords=[...this.selectedRecords][0]??null},toggleSelectedRecord(e){if(this.isRecordSelected(e)){this.deselectRecords([e]);return}this.selectRecords([e])},async selectAllRecords(){if(!h||l){this.isLoading=!0,this.selectedRecords=new Set(await d.getAllSelectableTableRecordKeys()),this.updatedSelectedRecords(),this.isLoading=!1;return}this.isTrackingDeselectedRecords=!0,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},canSelectAllRecords(){if(l){let i=this.getRecordsOnPage();return!this.areRecordsSelected(i)&&this.areRecordsToggleable(i)}let e=parseInt(this.$refs.allSelectableRecordsCount?.value);if(!e)return!1;let t=this.getSelectedRecordsCount();return e===t?!1:s===null||e<=s},deselectAllRecords(){this.isTrackingDeselectedRecords=!1,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},isRecordSelected(e){return this.isTrackingDeselectedRecords?!this.deselectedRecords.has(e):this.selectedRecords.has(e)},areRecordsSelected(e){return e.every(t=>this.isRecordSelected(t))},areRecordsToggleable(e){if(s===null||s===1)return!0;let t=e.filter(i=>this.isRecordSelected(i));return t.length===e.length?!0:this.getSelectedRecordsCount()+(e.length-t.length)<=s},toggleCollapseGroup(e){this.isGroupCollapsed(e)?o?this.groupVisibility.push(e):this.groupVisibility.splice(this.groupVisibility.indexOf(e),1):o?this.groupVisibility.splice(this.groupVisibility.indexOf(e),1):this.groupVisibility.push(e)},isGroupCollapsed(e){return o?!this.groupVisibility.includes(e):this.groupVisibility.includes(e)},resetCollapsedGroups(){this.groupVisibility=[]},watchForCheckboxClicks(){this.checkboxClickController&&this.checkboxClickController.abort(),this.checkboxClickController=new AbortController;let{signal:e}=this.checkboxClickController;this.$root?.addEventListener("click",t=>t.target?.matches(".fi-ta-record-checkbox")&&this.handleCheckboxClick(t,t.target),{signal:e})},handleCheckboxClick(e,t){if(!this.lastChecked){this.lastChecked=t;return}if(e.shiftKey){let i=Array.from(this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[]);if(!i.includes(this.lastChecked)){this.lastChecked=t;return}let n=i.indexOf(this.lastChecked),u=i.indexOf(t),g=[n,u].sort((c,m)=>c-m),a=[];for(let c=g[0];c<=g[1];c++)a.push(i[c].value);if(t.checked){if(!this.areRecordsToggleable(a)){t.checked=!1,this.deselectRecords([t.value]);return}this.selectRecords(a)}else this.deselectRecords(a)}this.lastChecked=t}});function f({columns:o,isLive:h}){return{error:void 0,isLoading:!1,columns:o,isLive:h,init(){if(!this.columns||this.columns.length===0){this.columns=[];return}},get groupedColumns(){let r={};return this.columns.filter(s=>s.type==="group").forEach(s=>{r[s.name]=this.calculateGroupedColumns(s)}),r},calculateGroupedColumns(r){if((r?.columns?.filter(t=>!t.isHidden)??[]).length===0)return{hidden:!0,checked:!1,disabled:!1,indeterminate:!1};let l=r.columns.filter(t=>!t.isHidden&&t.isToggleable!==!1);if(l.length===0)return{checked:!0,disabled:!0,indeterminate:!1};let d=l.filter(t=>t.isToggled).length,e=r.columns.filter(t=>!t.isHidden&&t.isToggleable===!1);return d===0&&e.length>0?{checked:!0,disabled:!1,indeterminate:!0}:d===0?{checked:!1,disabled:!1,indeterminate:!1}:d===l.length?{checked:!0,disabled:!1,indeterminate:!1}:{checked:!0,disabled:!1,indeterminate:!0}},getColumn(r,s=null){return s?this.columns.find(d=>d.type==="group"&&d.name===s)?.columns?.find(d=>d.name===r):this.columns.find(l=>l.name===r)},toggleGroup(r){let s=this.columns.find(i=>i.type==="group"&&i.name===r);if(!s?.columns)return;let l=this.calculateGroupedColumns(s);if(l.disabled)return;let e=s.columns.filter(i=>i.isToggleable!==!1).some(i=>i.isToggled),t=l.indeterminate?!0:!e;s.columns.filter(i=>i.isToggleable!==!1).forEach(i=>{i.isToggled=t}),this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},toggleColumn(r,s=null){let l=this.getColumn(r,s);!l||l.isToggleable===!1||(l.isToggled=!l.isToggled,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager())},reorderColumns(r){let s=r.map(l=>l.split("::"));this.reorderTopLevel(s),this.isLive&&this.applyTableColumnManager()},reorderGroupColumns(r,s){let l=this.columns.find(t=>t.type==="group"&&t.name===s);if(!l)return;let d=r.map(t=>t.split("::")),e=[];d.forEach(([t,i])=>{let n=l.columns.find(u=>u.name===i);n&&e.push(n)}),l.columns=e,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},reorderTopLevel(r){let s=this.columns,l=[];r.forEach(([d,e])=>{let t=s.find(i=>d==="group"?i.type==="group"&&i.name===e:d==="column"?i.type!=="group"&&i.name===e:!1);t&&l.push(t)}),this.columns=l},async applyTableColumnManager(){this.isLoading=!0;try{await this.$wire.call("applyTableColumnManager",this.columns),this.error=void 0}catch(r){this.error="Failed to update column visibility",console.error("Table toggle columns error:",r)}finally{this.isLoading=!1}}}}document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentTable",R),window.Alpine.data("filamentTableColumnManager",f)});})();
