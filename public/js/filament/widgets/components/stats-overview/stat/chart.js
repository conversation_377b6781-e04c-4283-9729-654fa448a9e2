var Zo=Object.defineProperty;var Jo=(i,t,e)=>t in i?Zo(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var M=(i,t,e)=>Jo(i,typeof t!="symbol"?t+"":t,e);function ye(i){return i+.5|0}var wt=(i,t,e)=>Math.max(Math.min(i,e),t);function xe(i){return wt(ye(i*2.55),0,255)}function St(i){return wt(ye(i*255),0,255)}function mt(i){return wt(ye(i/2.55)/100,0,1)}function zs(i){return wt(ye(i*100),0,100)}var nt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ai=[..."0123456789ABCDEF"],Qo=i=>Ai[i&15],ta=i=>Ai[(i&240)>>4]+Ai[i&15],Ye=i=>(i&240)>>4===(i&15),ea=i=>Ye(i.r)&&Ye(i.g)&&Ye(i.b)&&Ye(i.a);function ia(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&nt[i[1]]*17,g:255&nt[i[2]]*17,b:255&nt[i[3]]*17,a:t===5?nt[i[4]]*17:255}:(t===7||t===9)&&(e={r:nt[i[1]]<<4|nt[i[2]],g:nt[i[3]]<<4|nt[i[4]],b:nt[i[5]]<<4|nt[i[6]],a:t===9?nt[i[7]]<<4|nt[i[8]]:255})),e}var sa=(i,t)=>i<255?t(i):"";function na(i){var t=ea(i)?Qo:ta;return i?"#"+t(i.r)+t(i.g)+t(i.b)+sa(i.a,t):void 0}var oa=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Ns(i,t,e){let s=t*Math.min(e,1-e),n=(o,a=(o+i/30)%12)=>e-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function aa(i,t,e){let s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ra(i,t,e){let s=Ns(i,1,.5),n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function la(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Ti(i){let e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),a=Math.min(e,s,n),r=(o+a)/2,l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=la(e,s,n,h,o),l=l*60+.5),[l|0,c||0,r]}function Li(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(St)}function Ri(i,t,e){return Li(Ns,i,t,e)}function ca(i,t,e){return Li(ra,i,t,e)}function ha(i,t,e){return Li(aa,i,t,e)}function Hs(i){return(i%360+360)%360}function da(i){let t=oa.exec(i),e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?xe(+t[5]):St(+t[5]));let n=Hs(+t[2]),o=+t[3]/100,a=+t[4]/100;return t[1]==="hwb"?s=ca(n,o,a):t[1]==="hsv"?s=ha(n,o,a):s=Ri(n,o,a),{r:s[0],g:s[1],b:s[2],a:e}}function ua(i,t){var e=Ti(i);e[0]=Hs(e[0]+t),e=Ri(e),i.r=e[0],i.g=e[1],i.b=e[2]}function fa(i){if(!i)return;let t=Ti(i),e=t[0],s=zs(t[1]),n=zs(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${mt(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}var Bs={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Vs={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function ga(){let i={},t=Object.keys(Vs),e=Object.keys(Bs),s,n,o,a,r;for(s=0;s<t.length;s++){for(a=r=t[s],n=0;n<e.length;n++)o=e[n],r=r.replace(o,Bs[o]);o=parseInt(Vs[a],16),i[r]=[o>>16&255,o>>8&255,o&255]}return i}var Ue;function pa(i){Ue||(Ue=ga(),Ue.transparent=[0,0,0,0]);let t=Ue[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}var ma=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function ba(i){let t=ma.exec(i),e=255,s,n,o;if(t){if(t[7]!==s){let a=+t[7];e=t[8]?xe(a):wt(a*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?xe(s):wt(s,0,255)),n=255&(t[4]?xe(n):wt(n,0,255)),o=255&(t[6]?xe(o):wt(o,0,255)),{r:s,g:n,b:o,a:e}}}function xa(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${mt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}var Ci=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,qt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function _a(i,t,e){let s=qt(mt(i.r)),n=qt(mt(i.g)),o=qt(mt(i.b));return{r:St(Ci(s+e*(qt(mt(t.r))-s))),g:St(Ci(n+e*(qt(mt(t.g))-n))),b:St(Ci(o+e*(qt(mt(t.b))-o))),a:i.a+e*(t.a-i.a)}}function Xe(i,t,e){if(i){let s=Ti(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=Ri(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function js(i,t){return i&&Object.assign(t||{},i)}function Ws(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=St(i[3]))):(t=js(i,{r:0,g:0,b:0,a:1}),t.a=St(t.a)),t}function ya(i){return i.charAt(0)==="r"?ba(i):da(i)}var _e=class i{constructor(t){if(t instanceof i)return t;let e=typeof t,s;e==="object"?s=Ws(t):e==="string"&&(s=ia(t)||pa(t)||ya(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=js(this._rgb);return t&&(t.a=mt(t.a)),t}set rgb(t){this._rgb=Ws(t)}rgbString(){return this._valid?xa(this._rgb):void 0}hexString(){return this._valid?na(this._rgb):void 0}hslString(){return this._valid?fa(this._rgb):void 0}mix(t,e){if(t){let s=this.rgb,n=t.rgb,o,a=e===o?.5:e,r=2*a-1,l=s.a-n.a,c=((r*l===-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=_a(this._rgb,t._rgb,e)),this}clone(){return new i(this.rgb)}alpha(t){return this._rgb.a=St(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=ye(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Xe(this._rgb,2,t),this}darken(t){return Xe(this._rgb,2,-t),this}saturate(t){return Xe(this._rgb,1,t),this}desaturate(t){return Xe(this._rgb,1,-t),this}rotate(t){return ua(this._rgb,t),this}};function dt(){}var tn=(()=>{let i=0;return()=>i++})();function A(i){return i==null}function z(i){if(Array.isArray&&Array.isArray(i))return!0;let t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function T(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function N(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function J(i,t){return N(i)?i:t}function D(i,t){return typeof i>"u"?t:i}var en=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,zi=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function F(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function E(i,t,e,s){let n,o,a;if(z(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(T(i))for(a=Object.keys(i),o=a.length,n=0;n<o;n++)t.call(e,i[a[n]],a[n])}function ke(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Je(i){if(z(i))return i.map(Je);if(T(i)){let t=Object.create(null),e=Object.keys(i),s=e.length,n=0;for(;n<s;++n)t[e[n]]=Je(i[e[n]]);return t}return i}function sn(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function va(i,t,e,s){if(!sn(i))return;let n=t[i],o=e[i];T(n)&&T(o)?Zt(n,o,s):t[i]=Je(o)}function Zt(i,t,e){let s=z(t)?t:[t],n=s.length;if(!T(i))return i;e=e||{};let o=e.merger||va,a;for(let r=0;r<n;++r){if(a=s[r],!T(a))continue;let l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],i,a,e)}return i}function Qt(i,t){return Zt(i,t,{merger:Ma})}function Ma(i,t,e){if(!sn(i))return;let s=t[i],n=e[i];T(s)&&T(n)?Qt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Je(n))}var $s={"":i=>i,x:i=>i.x,y:i=>i.y};function ka(i){let t=i.split("."),e=[],s="";for(let n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function wa(i){let t=ka(i);return e=>{for(let s of t){if(s==="")break;e=e&&e[s]}return e}}function _t(i,t){return($s[t]||($s[t]=wa(t)))(i)}function ii(i){return i.charAt(0).toUpperCase()+i.slice(1)}var te=i=>typeof i<"u",bt=i=>typeof i=="function",Bi=(i,t)=>{if(i.size!==t.size)return!1;for(let e of i)if(!t.has(e))return!1;return!0};function nn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}var R=Math.PI,B=2*R,Sa=B+R,Qe=Number.POSITIVE_INFINITY,Pa=R/180,H=R/2,It=R/4,Ys=R*2/3,xt=Math.log10,lt=Math.sign;function ee(i,t,e){return Math.abs(i-t)<e}function Vi(i){let t=Math.round(i);i=ee(i,t,i/1e3)?t:i;let e=Math.pow(10,Math.floor(xt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function on(i){let t=[],e=Math.sqrt(i),s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Da(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function Bt(i){return!Da(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function an(i,t){let e=Math.round(i);return e-t<=i&&e+t>=i}function Wi(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ot(i){return i*(R/180)}function si(i){return i*(180/R)}function Ni(i){if(!N(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Hi(i,t){let e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s),o=Math.atan2(s,e);return o<-.5*R&&(o+=B),{angle:o,distance:n}}function ti(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Oa(i,t){return(i-t+Sa)%B-R}function X(i){return(i%B+B)%B}function ie(i,t,e,s){let n=X(i),o=X(t),a=X(e),r=X(o-n),l=X(a-n),c=X(n-o),h=X(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function Y(i,t,e){return Math.max(t,Math.min(e,i))}function rn(i){return Y(i,-32768,32767)}function ut(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function ni(i,t,e){e=e||(a=>i[a]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}var ct=(i,t,e,s)=>ni(i,e,s?n=>{let o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),ln=(i,t,e)=>ni(i,e,s=>i[s][t]>=e);function cn(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}var hn=["push","pop","shift","splice","unshift"];function dn(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),hn.forEach(e=>{let s="_onData"+ii(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){let a=n.apply(this,o);return i._chartjs.listeners.forEach(r=>{typeof r[s]=="function"&&r[s](...o)}),a}})})}function ji(i,t){let e=i._chartjs;if(!e)return;let s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(hn.forEach(o=>{delete i[o]}),delete i._chartjs)}function $i(i){let t=new Set(i);return t.size===i.length?i:Array.from(t)}var Yi=(function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame})();function Ui(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Yi.call(window,()=>{s=!1,i.apply(t,e)}))}}function un(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}var oi=i=>i==="start"?"left":i==="end"?"right":"center",K=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,fn=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function Xi(i,t,e){let s=t.length,n=0,o=s;if(i._sorted){let{iScale:a,vScale:r,_parsed:l}=i,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=a.axis,{min:d,max:u,minDefined:f,maxDefined:g}=a.getUserBounds();if(f){if(n=Math.min(ct(l,h,d).lo,e?s:ct(t,h,a.getPixelForValue(d)).lo),c){let p=l.slice(0,n+1).reverse().findIndex(m=>!A(m[r.axis]));n-=Math.max(0,p)}n=Y(n,0,s-1)}if(g){let p=Math.max(ct(l,a.axis,u,!0).hi+1,e?0:ct(t,h,a.getPixelForValue(u),!0).hi+1);if(c){let m=l.slice(p-1).findIndex(b=>!A(b[r.axis]));p+=Math.max(0,m)}o=Y(p,n,s)-n}else o=s-n}return{start:n,count:o}}function Ki(i){let{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;let o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}var Ke=i=>i===0||i===1,Us=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*B/e)),Xs=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*B/e)+1,Gt={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*H)+1,easeOutSine:i=>Math.sin(i*H),easeInOutSine:i=>-.5*(Math.cos(R*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>Ke(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>Ke(i)?i:Us(i,.075,.3),easeOutElastic:i=>Ke(i)?i:Xs(i,.075,.3),easeInOutElastic(i){return Ke(i)?i:i<.5?.5*Us(i*2,.1125,.45):.5+.5*Xs(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Gt.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Gt.easeInBounce(i*2)*.5:Gt.easeOutBounce(i*2-1)*.5+.5};function qi(i){if(i&&typeof i=="object"){let t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Gi(i){return qi(i)?i:new _e(i)}function Ei(i){return qi(i)?i:new _e(i).saturate(.5).darken(.1).hexString()}var Ca=["x","y","borderWidth","radius","tension"],Aa=["color","borderColor","backgroundColor"];function Ta(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Aa},numbers:{type:"number",properties:Ca}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function La(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}var Ks=new Map;function Ra(i,t){t=t||{};let e=i+JSON.stringify(t),s=Ks.get(e);return s||(s=new Intl.NumberFormat(i,t),Ks.set(e,s)),s}function se(i,t,e){return Ra(t,e).format(i)}var gn={values(i){return z(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";let s=this.chart.options.locale,n,o=i;if(e.length>1){let c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Ea(i,e)}let a=xt(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),se(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";let s=e[t].significand||i/Math.pow(10,Math.floor(xt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?gn.numeric.call(this,i,t,e):""}};function Ea(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var we={formatters:gn};function Ia(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:we.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}var Dt=Object.create(null),ai=Object.create(null);function ve(i,t){if(!t)return i;let e=t.split(".");for(let s=0,n=e.length;s<n;++s){let o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ii(i,t,e){return typeof t=="string"?Zt(ve(i,t),e):Zt(ve(i,""),t)}var Fi=class{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Ei(n.backgroundColor),this.hoverBorderColor=(s,n)=>Ei(n.borderColor),this.hoverColor=(s,n)=>Ei(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ii(this,t,e)}get(t){return ve(this,t)}describe(t,e){return Ii(ai,t,e)}override(t,e){return Ii(Dt,t,e)}route(t,e,s,n){let o=ve(this,t),a=ve(this,s),r="_"+e;Object.defineProperties(o,{[r]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){let l=this[r],c=a[n];return T(l)?Object.assign({},c,l):D(l,c)},set(l){this[r]=l}}})}apply(t){t.forEach(e=>e(this))}},V=new Fi({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Ta,La,Ia]);function Fa(i){return!i||A(i.size)||A(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Me(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function pn(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let a=0,r=e.length,l,c,h,d,u;for(l=0;l<r;l++)if(d=e[l],d!=null&&!z(d))a=Me(i,n,o,a,d);else if(z(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!z(u)&&(a=Me(i,n,o,a,u));i.restore();let f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return a}function Ot(i,t,e){let s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Zi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function ri(i,t,e,s){Ji(i,t,e,s,null)}function Ji(i,t,e,s,n){let o,a,r,l,c,h,d,u,f=t.pointStyle,g=t.rotation,p=t.radius,m=(g||0)*Pa;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,p,0,0,B):i.arc(e,s,p,0,B),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Ys,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=Ys,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,a=Math.cos(m+It)*l,d=Math.cos(m+It)*(n?n/2-c:l),r=Math.sin(m+It)*l,u=Math.sin(m+It)*(n?n/2-c:l),i.arc(e-d,s-r,c,m-R,m-H),i.arc(e+u,s-a,c,m-H,m),i.arc(e+d,s+r,c,m,m+H),i.arc(e-u,s+a,c,m+H,m+R),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=It;case"rectRot":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+u,s-a),i.lineTo(e+d,s+r),i.lineTo(e-u,s+a),i.closePath();break;case"crossRot":m+=It;case"cross":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"star":d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a),m+=It,d=Math.cos(m)*(n?n/2:p),a=Math.cos(m)*p,r=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"line":a=n?n/2:Math.cos(m)*p,r=Math.sin(m)*p,i.moveTo(e-a,s-r),i.lineTo(e+a,s+r);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function ht(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Se(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Pe(i){i.restore()}function mn(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){let o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function bn(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function za(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),A(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Ba(i,t,e,s,n){if(n.strikethrough||n.underline){let o=i.measureText(s),a=t-o.actualBoundingBoxLeft,r=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(a,h),i.lineTo(r,h),i.stroke()}}function Va(i,t){let e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Ct(i,t,e,s,n,o={}){let a=z(t)?t:[t],r=o.strokeWidth>0&&o.strokeColor!=="",l,c;for(i.save(),i.font=n.string,za(i,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&Va(i,o.backdrop),r&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),A(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Ba(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function ne(i,t){let{x:e,y:s,w:n,h:o,radius:a}=t;i.arc(e+a.topLeft,s+a.topLeft,a.topLeft,1.5*R,R,!0),i.lineTo(e,s+o-a.bottomLeft),i.arc(e+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,R,H,!0),i.lineTo(e+n-a.bottomRight,s+o),i.arc(e+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,H,0,!0),i.lineTo(e+n,s+a.topRight),i.arc(e+n-a.topRight,s+a.topRight,a.topRight,0,-H,!0),i.lineTo(e+a.topLeft,s)}var Wa=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Na=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Ha(i,t){let e=(""+i).match(Wa);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}var ja=i=>+i||0;function li(i,t){let e={},s=T(t),n=s?Object.keys(t):t,o=T(i)?s?a=>D(i[a],i[t[a]]):a=>i[a]:()=>i;for(let a of n)e[a]=ja(o(a));return e}function Qi(i){return li(i,{top:"y",right:"x",bottom:"y",left:"x"})}function At(i){return li(i,["topLeft","topRight","bottomLeft","bottomRight"])}function q(i){let t=Qi(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function $(i,t){i=i||{},t=t||V.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(Na)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let n={family:D(i.family,t.family),lineHeight:Ha(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=Fa(n),n}function oe(i,t,e,s){let n=!0,o,a,r;for(o=0,a=i.length;o<a;++o)if(r=i[o],r!==void 0&&(t!==void 0&&typeof r=="function"&&(r=r(t),n=!1),e!==void 0&&z(r)&&(r=r[e%r.length],n=!1),r!==void 0))return s&&!n&&(s.cacheable=!1),r}function xn(i,t,e){let{min:s,max:n}=i,o=zi(t,(n-s)/2),a=(r,l)=>e&&r===0?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}function yt(i,t){return Object.assign(Object.create(i),t)}function ci(i,t=[""],e,s,n=()=>i[0]){let o=e||i;typeof s>"u"&&(s=vn("_fallback",i));let a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>ci([r,...i],t,o,s)};return new Proxy(a,{deleteProperty(r,l){return delete r[l],delete r._keys,delete i[0][l],!0},get(r,l){return _n(r,l,()=>Za(l,t,i,r))},getOwnPropertyDescriptor(r,l){return Reflect.getOwnPropertyDescriptor(r._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(r,l){return Gs(r).includes(l)},ownKeys(r){return Gs(r)},set(r,l,c){let h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function zt(i,t,e,s){let n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:ts(i,s),setContext:o=>zt(i,o,e,s),override:o=>zt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,a){return delete o[a],delete i[a],!0},get(o,a,r){return _n(o,a,()=>Ya(o,a,r))},getOwnPropertyDescriptor(o,a){return o._descriptors.allKeys?Reflect.has(i,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,a)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,a){return Reflect.has(i,a)},ownKeys(){return Reflect.ownKeys(i)},set(o,a,r){return i[a]=r,delete o[a],!0}})}function ts(i,t={scriptable:!0,indexable:!0}){let{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:bt(e)?e:()=>e,isIndexable:bt(s)?s:()=>s}}var $a=(i,t)=>i?i+ii(t):t,es=(i,t)=>T(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function _n(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];let s=e();return i[t]=s,s}function Ya(i,t,e){let{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=i,r=s[t];return bt(r)&&a.isScriptable(t)&&(r=Ua(t,r,i,e)),z(r)&&r.length&&(r=Xa(t,r,i,a.isIndexable)),es(t,r)&&(r=zt(r,n,o&&o[t],a)),r}function Ua(i,t,e,s){let{_proxy:n,_context:o,_subProxy:a,_stack:r}=e;if(r.has(i))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+i);r.add(i);let l=t(o,a||s);return r.delete(i),es(i,l)&&(l=is(n._scopes,n,i,l)),l}function Xa(i,t,e,s){let{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(T(t[0])){let l=t,c=n._scopes.filter(h=>h!==l);t=[];for(let h of l){let d=is(c,n,i,h);t.push(zt(d,o,a&&a[i],r))}}return t}function yn(i,t,e){return bt(i)?i(t,e):i}var Ka=(i,t)=>i===!0?t:typeof i=="string"?_t(t,i):void 0;function qa(i,t,e,s,n){for(let o of t){let a=Ka(e,o);if(a){i.add(a);let r=yn(a._fallback,e,n);if(typeof r<"u"&&r!==e&&r!==s)return r}else if(a===!1&&typeof s<"u"&&e!==s)return null}return!1}function is(i,t,e,s){let n=t._rootScopes,o=yn(t._fallback,e,s),a=[...i,...n],r=new Set;r.add(s);let l=qs(r,a,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=qs(r,a,o,l,s),l===null)?!1:ci(Array.from(r),[""],n,o,()=>Ga(t,e,s))}function qs(i,t,e,s,n){for(;e;)e=qa(i,t,e,s,n);return e}function Ga(i,t,e){let s=i._getTarget();t in s||(s[t]={});let n=s[t];return z(n)&&T(e)?e:n||{}}function Za(i,t,e,s){let n;for(let o of t)if(n=vn($a(o,i),e),typeof n<"u")return es(i,n)?is(e,s,i,n):n}function vn(i,t){for(let e of t){if(!e)continue;let s=e[i];if(typeof s<"u")return s}}function Gs(i){let t=i._keys;return t||(t=i._keys=Ja(i._scopes)),t}function Ja(i){let t=new Set;for(let e of i)for(let s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function ss(i,t,e,s){let{iScale:n}=i,{key:o="r"}=this._parsing,a=new Array(s),r,l,c,h;for(r=0,l=s;r<l;++r)c=r+e,h=t[c],a[r]={r:n.parse(_t(h,o),c)};return a}var Qa=Number.EPSILON||1e-14,Jt=(i,t)=>t<i.length&&!i[t].skip&&i[t],Mn=i=>i==="x"?"y":"x";function tr(i,t,e,s){let n=i.skip?t:i,o=t,a=e.skip?t:e,r=ti(o,n),l=ti(a,o),c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let d=s*c,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function er(i,t,e){let s=i.length,n,o,a,r,l,c=Jt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Jt(i,h+1),!(!l||!c)){if(ee(t[h],0,Qa)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),e[h]=n*a*t[h],e[h+1]=o*a*t[h])}}function ir(i,t,e="x"){let s=Mn(e),n=i.length,o,a,r,l=Jt(i,0);for(let c=0;c<n;++c){if(a=r,r=l,l=Jt(i,c+1),!r)continue;let h=r[e],d=r[s];a&&(o=(h-a[e])/3,r[`cp1${e}`]=h-o,r[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,r[`cp2${e}`]=h+o,r[`cp2${s}`]=d+o*t[c])}}function sr(i,t="x"){let e=Mn(t),s=i.length,n=Array(s).fill(0),o=Array(s),a,r,l,c=Jt(i,0);for(a=0;a<s;++a)if(r=l,l=c,c=Jt(i,a+1),!!l){if(c){let h=c[t]-l[t];n[a]=h!==0?(c[e]-l[e])/h:0}o[a]=r?c?lt(n[a-1])!==lt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}er(i,n,o),ir(i,o,t)}function qe(i,t,e){return Math.max(Math.min(i,e),t)}function nr(i,t){let e,s,n,o,a,r=ht(i[0],t);for(e=0,s=i.length;e<s;++e)a=o,o=r,r=e<s-1&&ht(i[e+1],t),o&&(n=i[e],a&&(n.cp1x=qe(n.cp1x,t.left,t.right),n.cp1y=qe(n.cp1y,t.top,t.bottom)),r&&(n.cp2x=qe(n.cp2x,t.left,t.right),n.cp2y=qe(n.cp2y,t.top,t.bottom)))}function kn(i,t,e,s,n){let o,a,r,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")sr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,a=i.length;o<a;++o)r=i[o],l=tr(c,r,i[Math.min(o+1,a-(s?0:1))%a],t.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}t.capBezierPoints&&nr(i,e)}function hi(){return typeof window<"u"&&typeof document<"u"}function di(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function ei(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}var ui=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function or(i,t){return ui(i).getPropertyValue(t)}var ar=["top","right","bottom","left"];function Ft(i,t,e){let s={};e=e?"-"+e:"";for(let n=0;n<4;n++){let o=ar[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}var rr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function lr(i,t){let e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s,a=!1,r,l;if(rr(n,o,i.target))r=n,l=o;else{let c=t.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}function Tt(i,t){if("native"in i)return i;let{canvas:e,currentDevicePixelRatio:s}=t,n=ui(e),o=n.boxSizing==="border-box",a=Ft(n,"padding"),r=Ft(n,"border","width"),{x:l,y:c,box:h}=lr(i,e),d=a.left+(h&&r.left),u=a.top+(h&&r.top),{width:f,height:g}=t;return o&&(f-=a.width+r.width,g-=a.height+r.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function cr(i,t,e){let s,n;if(t===void 0||e===void 0){let o=i&&di(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{let a=o.getBoundingClientRect(),r=ui(o),l=Ft(r,"border","width"),c=Ft(r,"padding");t=a.width-c.width-l.width,e=a.height-c.height-l.height,s=ei(r.maxWidth,o,"clientWidth"),n=ei(r.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Qe,maxHeight:n||Qe}}var Ge=i=>Math.round(i*10)/10;function wn(i,t,e,s){let n=ui(i),o=Ft(n,"margin"),a=ei(n.maxWidth,i,"clientWidth")||Qe,r=ei(n.maxHeight,i,"clientHeight")||Qe,l=cr(i,t,e),{width:c,height:h}=l;if(n.boxSizing==="content-box"){let u=Ft(n,"border","width"),f=Ft(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=Ge(Math.min(c,a,l.maxWidth)),h=Ge(Math.min(h,r,l.maxHeight)),c&&!h&&(h=Ge(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=Ge(Math.floor(h*s))),{width:c,height:h}}function ns(i,t,e){let s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);let a=i.canvas;return a.style&&(e||!a.style.height&&!a.style.width)&&(a.style.height=`${i.height}px`,a.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||a.height!==n||a.width!==o?(i.currentDevicePixelRatio=s,a.height=n,a.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}var Sn=(function(){let i=!1;try{let t={get passive(){return i=!0,!1}};hi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i})();function os(i,t){let e=or(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Pt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Pn(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Dn(i,t,e,s){let n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},a=Pt(i,n,e),r=Pt(n,o,e),l=Pt(o,t,e),c=Pt(a,r,e),h=Pt(r,l,e);return Pt(c,h,e)}var hr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},dr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Vt(i,t,e){return i?hr(t,e):dr()}function as(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function rs(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function On(i){return i==="angle"?{between:ie,compare:Oa,normalize:X}:{between:ut,compare:(t,e)=>t-e,normalize:t=>t}}function Zs({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function ur(i,t,e){let{property:s,start:n,end:o}=e,{between:a,normalize:r}=On(s),l=t.length,{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&a(r(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function ls(i,t,e){if(!e)return[i];let{property:s,start:n,end:o}=e,a=t.length,{compare:r,between:l,normalize:c}=On(s),{start:h,end:d,loop:u,style:f}=ur(i,t,e),g=[],p=!1,m=null,b,x,v,y=()=>l(n,v,b)&&r(n,v)!==0,_=()=>r(o,b)===0||l(o,v,b),k=()=>p||y(),w=()=>!p||_();for(let S=h,P=h;S<=d;++S)x=t[S%a],!x.skip&&(b=c(x[s]),b!==v&&(p=l(b,n,o),m===null&&k()&&(m=r(b,n)===0?S:P),m!==null&&w()&&(g.push(Zs({start:m,end:S,loop:u,count:a,style:f})),m=null),P=S,v=b));return m!==null&&g.push(Zs({start:m,end:d,loop:u,count:a,style:f})),g}function cs(i,t){let e=[],s=i.segments;for(let n=0;n<s.length;n++){let o=ls(s[n],i.points,t);o.length&&e.push(...o)}return e}function fr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function gr(i,t,e,s){let n=i.length,o=[],a=t,r=i[t],l;for(l=t+1;l<=e;++l){let c=i[l%n];c.skip||c.stop?r.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=a=c.stop?l:null):(a=l,r.skip&&(t=l)),r=c}return a!==null&&o.push({start:t%n,end:a%n,loop:s}),o}function Cn(i,t){let e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];let o=!!i._loop,{start:a,end:r}=fr(e,n,o,s);if(s===!0)return Js(i,[{start:a,end:r,loop:o}],e,t);let l=r<a?r+n:r,c=!!i._fullLoop&&a===0&&r===n-1;return Js(i,gr(e,a,l,c),e,t)}function Js(i,t,e,s){return!s||!s.setContext||!e?t:pr(i,t,e,s)}function pr(i,t,e,s){let n=i._chart.getContext(),o=Qs(i.options),{_datasetIndex:a,options:{spanGaps:r}}=i,l=e.length,c=[],h=o,d=t[0].start,u=d;function f(g,p,m,b){let x=r?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=x;for(;e[p%l].skip;)p+=x;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(let g of t){d=r?d:g.start;let p=e[d%l],m;for(u=d+1;u<=g.end;u++){let b=e[u%l];m=Qs(s.setContext(yt(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),mr(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function Qs(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function mr(i,t){if(!t)return!1;let e=[],s=function(n,o){return qi(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function Ze(i,t,e){return i.options.clip?i[e]:t[e]}function br(i,t){let{xScale:e,yScale:s}=i;return e&&s?{left:Ze(e,t,"left"),right:Ze(e,t,"right"),top:Ze(s,t,"top"),bottom:Ze(s,t,"bottom")}:t}function hs(i,t){let e=t._clip;if(e.disabled)return!1;let s=br(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}var Ms=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){let o=e.listeners[n],a=e.duration;o.forEach(r=>r({chart:t,initial:e.initial,numSteps:a,currentStep:Math.min(s-e.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Yi.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;let o=s.items,a=o.length-1,r=!1,l;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){let e=this._charts,s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let s=e.items,n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}},vt=new Ms,An="transparent",xr={boolean(i,t,e){return e>.5?t:i},color(i,t,e){let s=Gi(i||An),n=s.valid&&Gi(t||An);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}},ks=class{constructor(t,e,s,n){let o=e[s];n=oe([t.to,n,o,t.from]);let a=oe([t.from,o,n]);this._active=!0,this._fn=t.fn||xr[t.type||typeof a],this._easing=Gt[t.easing]||Gt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);let n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=oe([t.to,e,n,t.from]),this._from=oe([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e=t-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to,l;if(this._active=o!==r&&(a||e<s),!this._active){this._target[n]=r,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){let e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}},vi=class{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!T(t))return;let e=Object.keys(V.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{let o=t[n];if(!T(o))return;let a={};for(let r of e)a[r]=o[r];(z(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(t,e){let s=e.options,n=yr(t,s);if(!n)return[];let o=this._createAnimations(n,s);return s.$shared&&_r(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){let s=this._properties,n=[],o=t.$animations||(t.$animations={}),a=Object.keys(e),r=Date.now(),l;for(l=a.length-1;l>=0;--l){let c=a[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}let h=e[c],d=o[c],u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,r);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new ks(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}let s=this._createAnimations(t,e);if(s.length)return vt.add(this._chart,s),!0}};function _r(i,t){let e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){let o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function yr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Tn(i,t){let e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function vr(i,t,e){if(e===!1)return!1;let s=Tn(i,e),n=Tn(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Mr(i){let t,e,s,n;return T(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Ao(i,t){let e=[],s=i._getSortedDatasetMetas(t),n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Ln(i,t,e,s={}){let n=i.keys,o=s.mode==="single",a,r,l,c;if(t===null)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===e){if(h=!0,s.all)continue;break}c=i.values[l],N(c)&&(o||t===0||lt(t)===lt(c))&&(t+=c)}return!h&&!s.all?0:t}function kr(i,t){let{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",a=Object.keys(i),r=new Array(a.length),l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:i[h]};return r}function ds(i,t){let e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function wr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Sr(i){let{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Pr(i,t,e){let s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Rn(i,t,e,s){for(let n of t.getMatchingVisibleMetas(s).reverse()){let o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function En(i,t){let{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=wr(o,a,s),d=t.length,u;for(let f=0;f<d;++f){let g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=Pr(n,h,p),u[r]=m,u._top=Rn(u,a,!0,s.type),u._bottom=Rn(u,a,!1,s.type);let x=u._visualValues||(u._visualValues={});x[r]=m}}function us(i,t){let e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Dr(i,t){return yt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Or(i,t,e){return yt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function De(i,t){let e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(let n of t){let o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}var fs=i=>i==="reset"||i==="none",In=(i,t)=>t?i:Object.assign({},i),Cr=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Ao(e,!0),values:null},it=class{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ds(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&De(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=D(s.xAxisID,us(t,"x")),a=e.yAxisID=D(s.yAxisID,us(t,"y")),r=e.rAxisID=D(s.rAxisID,us(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,a,r),h=e.vAxisID=n(l,a,o,r);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(a),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&ji(this._data,this),t._stacked&&De(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(T(e)){let n=this._cachedMeta;this._data=kr(e,n)}else if(s!==e){if(s){ji(s,this);let n=this._cachedMeta;De(n),n._parsed=[]}e&&Object.isExtensible(e)&&dn(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,s=this.getDataset(),n=!1;this._dataCheck();let o=e._stacked;e._stacked=ds(e.vScale,e),e.stack!==s.stack&&(n=!0,De(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(En(this,e._parsed),e._stacked=ds(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:a}=s,r=o.axis,l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{z(n[t])?u=this.parseArrayData(s,n,t,e):T(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);let f=()=>d[r]===null||c&&d[r]<c[r];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}a&&En(this,u)}parsePrimitiveData(t,e,s,n){let{iScale:o,vScale:a}=t,r=o.axis,l=a.axis,c=o.getLabels(),h=o===a,d=new Array(n),u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[r]:h||o.parse(c[g],g),[l]:a.parse(e[g],g)};return d}parseArrayData(t,e,s,n){let{xScale:o,yScale:a}=t,r=new Array(n),l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],r[l]={x:o.parse(d[0],h),y:a.parse(d[1],h)};return r}parseObjectData(t,e,s,n){let{xScale:o,yScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=new Array(n),h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(_t(f,r),u),y:a.parse(_t(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){let n=this.chart,o=this._cachedMeta,a=e[t.axis],r={keys:Ao(n,!0),values:e._stacks[t.axis]._visualValues};return Ln(r,a,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){let o=s[e.axis],a=o===null?NaN:o,r=n&&s._stacks[e.axis];n&&r&&(n.values=r,a=Ln(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,e){let s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,a=n.length,r=this._getOtherScale(t),l=Cr(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Sr(r),u,f;function g(){f=n[u];let p=f[r.axis];return!N(f[t.axis])||h>p||d<p}for(u=0;u<a&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=a-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){let e=this._cachedMeta._parsed,s=[],n,o,a;for(n=0,o=e.length;n<o;++n)a=e[n][t.axis],N(a)&&s.push(a);return s}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){let e=this._cachedMeta;this.update(t||"default"),e._clip=Mr(D(this.options.clip,vr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,a=[],r=this._drawStart||0,l=this._drawCount||n.length-r,c=this.options.drawActiveElementsOnTop,h;for(s.dataset&&s.dataset.draw(t,o,r,l),h=r;h<r+l;++h){let d=n[h];d.hidden||(d.active&&c?a.push(d):d.draw(t,o))}for(h=0;h<a.length;++h)a[h].draw(t,o)}getStyle(t,e){let s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){let n=this.getDataset(),o;if(t>=0&&t<this._cachedMeta.data.length){let a=this._cachedMeta.data[t];o=a.$context||(a.$context=Or(this.getContext(),t,a)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Dr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){let n=e==="active",o=this._cachedDataOpts,a=t+"-"+e,r=o[a],l=this.enableOptionSharing&&te(s);if(r)return In(r,l);let c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(V.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[a]=Object.freeze(In(p,l))),p}_resolveAnimations(t,e,s){let n=this.chart,o=this._cachedDataOpts,a=`animation-${e}`,r=o[a];if(r)return r;let l;if(n.options.animation!==!1){let h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}let c=new vi(n,l&&l.animations);return l&&l._cacheable&&(o[a]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||fs(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),a=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:a}}updateElement(t,e,s,n){fs(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!fs(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;let o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,s=this._cachedMeta.data;for(let[r,l,c]of this._syncList)this[r](l,c);this._syncList=[];let n=s.length,o=e.length,a=Math.min(o,n);a&&this.parse(0,a),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){let n=this._cachedMeta,o=n.data,a=t+e,r,l=c=>{for(c.length+=e,r=c.length-1;r>=a;r--)c[r]=c[r-e]};for(l(o),r=t;r<a;++r)o[r]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){let s=this._cachedMeta;if(this._parsing){let n=s._parsed.splice(t,e);s._stacked&&De(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}};M(it,"defaults",{}),M(it,"datasetElementType",null),M(it,"dataElementType",null);function Ar(i,t){if(!i._cache.$bar){let e=i.getMatchingVisibleMetas(t),s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=$i(s.sort((n,o)=>n-o))}return i._cache.$bar}function Tr(i){let t=i.iScale,e=Ar(t,i.type),s=t._length,n,o,a,r,l=()=>{a===32767||a===-32768||(te(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=e.length;n<o;++n)a=t.getPixelForValue(e[n]),l();for(r=void 0,n=0,o=t.ticks.length;n<o;++n)a=t.getPixelForTick(n),l();return s}function Lr(i,t,e,s){let n=e.barThickness,o,a;return A(n)?(o=t.min*e.categoryPercentage,a=e.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:t.pixels[i]-o/2}}function Rr(i,t,e,s){let n=t.pixels,o=n[i],a=i>0?n[i-1]:null,r=i<n.length-1?n[i+1]:null,l=e.categoryPercentage;a===null&&(a=o-(r===null?t.end-t.start:r-o)),r===null&&(r=o+o-a);let c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:e.barPercentage,start:c}}function Er(i,t,e,s){let n=e.parse(i[0],s),o=e.parse(i[1],s),a=Math.min(n,o),r=Math.max(n,o),l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}function To(i,t,e,s){return z(i)?Er(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Fn(i,t,e,s){let n=i.iScale,o=i.vScale,a=n.getLabels(),r=n===o,l=[],c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(To(u,d,o,c));return l}function gs(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function Ir(i,t,e){return i!==0?lt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Fr(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function zr(i,t,e,s){let n=t.borderSkipped,o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:a,end:r,reverse:l,top:c,bottom:h}=Fr(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[zn(h,a,r,l)]=!0,n=c)),o[zn(n,a,r,l)]=!0,i.borderSkipped=o}function zn(i,t,e,s){return s?(i=Br(i,t,e),i=Bn(i,e,t)):i=Bn(i,t,e),i}function Br(i,t,e){return i===t?e:i===e?t:i}function Bn(i,t,e){return i==="start"?t:i==="end"?e:i}function Vr(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}var re=class extends it{parsePrimitiveData(t,e,s,n){return Fn(t,e,s,n)}parseArrayData(t,e,s,n){return Fn(t,e,s,n)}parseObjectData(t,e,s,n){let{iScale:o,vScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?r:l,h=a.axis==="x"?r:l,d=[],u,f,g,p;for(u=s,f=s+n;u<f;++u)p=e[u],g={},g[o.axis]=o.parse(_t(p,c),u),d.push(To(_t(p,h),g,a,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);let o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),a=o._custom,r=gs(a)?"["+a.start+", "+a.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){let o=n==="reset",{index:a,_cachedMeta:{vScale:r}}=this,l=r.getBasePixel(),c=r.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){let g=this.getParsed(f),p=o||A(g[r.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[r.axis],x={horizontal:c,base:p.base,enableBorderRadius:!b||gs(g._custom)||a===b._top||a===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};u&&(x.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));let v=x.options||t[f].options;zr(x,v,b,a),Vr(x,v,h.ratio),this.updateElement(t[f],f,x,n)}}_getStacks(t,e){let{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,a=[],r=this._cachedMeta.controller.getParsed(e),l=r&&r[s.axis],c=h=>{let d=h._parsed.find(f=>f[s.axis]===l),u=d&&d[h.vScale.axis];if(A(u)||isNaN(u))return!0};for(let h of n)if(!(e!==void 0&&c(h))&&((o===!1||a.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&a.push(h.stack),h.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){let t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(s=>t[s].axis===e).shift()}_getAxis(){let t={},e=this.getFirstScaleIdForIndexAxis();for(let s of this.chart.data.datasets)t[D(this.chart.options.indexAxis==="x"?s.xAxisID:s.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,s){let n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){let t=this.options,e=this._cachedMeta,s=e.iScale,n=[],o,a;for(o=0,a=e.data.length;o<a;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));let r=t.barThickness;return{min:r||Tr(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:r?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:a}}=this,r=o||0,l=this.getParsed(t),c=l._custom,h=gs(c),d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,g,p;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&lt(d)!==lt(c.barEnd)&&(u=0),u+=d);let m=!A(o)&&!h?o:u,b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(u+f):g=b,p=g-b,Math.abs(p)<a){p=Ir(p,e,r)*a,d===r&&(b-=p/2);let x=e.getPixelForDecimal(0),v=e.getPixelForDecimal(1),y=Math.min(x,v),_=Math.max(x,v);b=Math.max(Math.min(b,_),y),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(r)){let x=lt(p)*e.getLineWidthForValue(r)/2;b+=x,p-=x}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){let s=e.scale,n=this.options,o=n.skipNull,a=D(n.maxBarThickness,1/0),r,l,c=this._getAxisCount();if(e.grouped){let h=o?this._getStackCount(t):e.stackCount,d=n.barThickness==="flex"?Rr(t,e,n,h*c):Lr(t,e,n,h*c),u=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,f=this._getAxis().indexOf(D(u,this.getFirstScaleIdForIndexAxis())),g=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0)+f;r=d.start+d.chunk*g+d.chunk/2,l=Math.min(a,d.chunk*d.ratio)}else r=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(a,e.min*e.ratio);return{base:r-l/2,head:r+l/2,center:r,size:l}}draw(){let t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length,o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}};M(re,"id","bar"),M(re,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),M(re,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});var le=class extends it{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,s,n){let o=super.parsePrimitiveData(t,e,s,n);for(let a=0;a<o.length;a++)o[a]._custom=this.resolveDataElementOptions(a+s).radius;return o}parseArrayData(t,e,s,n){let o=super.parseArrayData(t,e,s,n);for(let a=0;a<o.length;a++){let r=e[s+a];o[a]._custom=D(r[2],this.resolveDataElementOptions(a+s).radius)}return o}parseObjectData(t,e,s,n){let o=super.parseObjectData(t,e,s,n);for(let a=0;a<o.length;a++){let r=e[s+a];o[a]._custom=D(r&&r.r&&+r.r,this.resolveDataElementOptions(a+s).radius)}return o}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y),c=a._custom;return{label:s[t]||"",value:"("+r+", "+l+(c?", "+c:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,n){let o=n==="reset",{iScale:a,vScale:r}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=a.axis,d=r.axis;for(let u=e;u<e+s;u++){let f=t[u],g=!o&&this.getParsed(u),p={},m=p[h]=o?a.getPixelForDecimal(.5):a.getPixelForValue(g[h]),b=p[d]=o?r.getBasePixel():r.getPixelForValue(g[d]);p.skip=isNaN(m)||isNaN(b),c&&(p.options=l||this.resolveDataElementOptions(u,f.active?"active":n),o&&(p.options.radius=0)),this.updateElement(f,u,p,n)}}resolveDataElementOptions(t,e){let s=this.getParsed(t),n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));let o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=D(s&&s._custom,o),n}};M(le,"id","bubble"),M(le,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),M(le,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function Wr(i,t,e){let s=1,n=1,o=0,a=0;if(t<B){let r=i,l=r+t,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),u=Math.sin(l),f=(v,y,_)=>ie(v,r,l,!0)?1:Math.max(y,y*e,_,_*e),g=(v,y,_)=>ie(v,r,l,!0)?-1:Math.min(y,y*e,_,_*e),p=f(0,c,d),m=f(H,h,u),b=g(R,c,d),x=g(R+H,h,u);s=(p-b)/2,n=(m-x)/2,o=-(p+b)/2,a=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}var kt=class extends it{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(T(s[t])){let{key:l="value"}=this._parsing;o=c=>+_t(s[c],l)}let a,r;for(a=t,r=t+e;a<r;++a)n._parsed[a]=o(a)}}_getRotation(){return ot(this.options.rotation-90)}_getCircumference(){return ot(this.options.circumference)}_getRotationExtents(){let t=B,e=-B;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){let n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),a=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+a)}return{rotation:t,circumference:e-t}}update(t){let e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,a=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,r=Math.max((Math.min(s.width,s.height)-a)/2,0),l=Math.min(en(this.options.cutout,r),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:p}=Wr(d,h,l),m=(s.width-a)/u,b=(s.height-a)/f,x=Math.max(Math.min(m,b)/2,0),v=zi(this.options.radius,x),y=Math.max(v*l,0),_=(v-y)/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=p*v,n.total=this.calculateTotal(),this.outerRadius=v-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){let s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/B)}updateElements(t,e,s,n){let o=n==="reset",a=this.chart,r=a.chartArea,c=a.options.animation,h=(r.left+r.right)/2,d=(r.top+r.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n),b=this._getRotation(),x;for(x=0;x<e;++x)b+=this._circumference(x,o);for(x=e;x<e+s;++x){let v=this._circumference(x,o),y=t[x],_={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+v,circumference:v,outerRadius:g,innerRadius:f};m&&(_.options=p||this.resolveDataElementOptions(x,y.active?"active":n)),b+=v,this.updateElement(y,x,_,n)}}calculateTotal(){let t=this._cachedMeta,e=t.data,s=0,n;for(n=0;n<e.length;n++){let o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?B*(Math.abs(t)/e):0}getLabelAndValue(t){let e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=se(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0,s=this.chart,n,o,a,r,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){a=s.getDatasetMeta(n),t=a.data,r=a.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=r.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){let o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(D(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}};M(kt,"id","doughnut"),M(kt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),M(kt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),M(kt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{let l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});var ce=class extends it{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,a=this.chart._animationsDisabled,{start:r,count:l}=Xi(e,n,a);this._drawStart=r,this._drawCount=l,Ki(e)&&(r=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;let c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!a,options:c},t),this.updateElements(n,r,l,t)}updateElements(t,e,s,n){let o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=a.axis,f=r.axis,{spanGaps:g,segment:p}=this.options,m=Bt(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,v=t.length,y=e>0&&this.getParsed(e-1);for(let _=0;_<v;++_){let k=t[_],w=b?k:{};if(_<e||_>=x){w.skip=!0;continue}let S=this.getParsed(_),P=A(S[f]),O=w[u]=a.getPixelForValue(S[u],_),C=w[f]=o||P?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,S,l):S[f],_);w.skip=isNaN(O)||isNaN(C)||P,w.stop=_>0&&Math.abs(S[u]-y[u])>m,p&&(w.parsed=S,w.raw=c.data[_]),d&&(w.options=h||this.resolveDataElementOptions(_,k.active?"active":n)),b||this.updateElement(k,_,w,n),y=S}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;let o=n[0].size(this.resolveDataElementOptions(0)),a=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,a)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}};M(ce,"id","line"),M(ce,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),M(ce,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});var $t=class extends it{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=se(e._parsed[t].r,s.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,s,n){return ss.bind(this)(t,e,s,n)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,n)=>{let o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,s=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),a=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),r=(o-a)/t.getVisibleDatasetCount();this.outerRadius=o-r*this.index,this.innerRadius=this.outerRadius-r}updateElements(t,e,s,n){let o=n==="reset",a=this.chart,l=a.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,u=c.getIndexAngle(0)-.5*R,f=u,g,p=360/this.countVisibleElements();for(g=0;g<e;++g)f+=this._computeAngle(g,n,p);for(g=e;g<e+s;g++){let m=t[g],b=f,x=f+this._computeAngle(g,n,p),v=a.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=x,o&&(l.animateScale&&(v=0),l.animateRotate&&(b=x=u));let y={x:h,y:d,innerRadius:0,outerRadius:v,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(g,m.active?"active":n)};this.updateElement(m,g,y,n)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((s,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?ot(this.resolveDataElementOptions(t,e).angle||s):0}};M($t,"id","polarArea"),M($t,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),M($t,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{let l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});var Le=class extends kt{};M(Le,"id","pie"),M(Le,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});var he=class extends it{getLabelAndValue(t){let e=this._cachedMeta.vScale,s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,n){return ss.bind(this)(t,e,s,n)}update(t){let e=this._cachedMeta,s=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(s.points=n,t!=="resize"){let a=this.resolveDatasetElementOptions(t);this.options.showLine||(a.borderWidth=0);let r={_loop:!0,_fullLoop:o.length===n.length,options:a};this.updateElement(s,void 0,r,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,s,n){let o=this._cachedMeta.rScale,a=n==="reset";for(let r=e;r<e+s;r++){let l=t[r],c=this.resolveDataElementOptions(r,l.active?"active":n),h=o.getPointPositionForValue(r,this.getParsed(r).r),d=a?o.xCenter:h.x,u=a?o.yCenter:h.y,f={x:d,y:u,angle:h.angle,skip:isNaN(d)||isNaN(u),options:c};this.updateElement(l,r,f,n)}}};M(he,"id","radar"),M(he,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),M(he,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});var de=class extends it{getLabelAndValue(t){let e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y);return{label:s[t]||"",value:"("+r+", "+l+")"}}update(t){let e=this._cachedMeta,{data:s=[]}=e,n=this.chart._animationsDisabled,{start:o,count:a}=Xi(e,s,n);if(this._drawStart=o,this._drawCount=a,Ki(e)&&(o=0,a=s.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:r,_dataset:l}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!l._decimated,r.points=s;let c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(r,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(s,o,a,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,s,n){let o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),d=this.getSharedOptions(h),u=this.includeOptions(n,d),f=a.axis,g=r.axis,{spanGaps:p,segment:m}=this.options,b=Bt(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||n==="none",v=e>0&&this.getParsed(e-1);for(let y=e;y<e+s;++y){let _=t[y],k=this.getParsed(y),w=x?_:{},S=A(k[g]),P=w[f]=a.getPixelForValue(k[f],y),O=w[g]=o||S?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,k,l):k[g],y);w.skip=isNaN(P)||isNaN(O)||S,w.stop=y>0&&Math.abs(k[f]-v[f])>b,m&&(w.parsed=k,w.raw=c.data[y]),u&&(w.options=d||this.resolveDataElementOptions(y,_.active?"active":n)),x||this.updateElement(_,y,w,n),v=k}this.updateSharedOptions(d,n,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let r=0;for(let l=e.length-1;l>=0;--l)r=Math.max(r,e[l].size(this.resolveDataElementOptions(l))/2);return r>0&&r}let s=t.dataset,n=s.options&&s.options.borderWidth||0;if(!e.length)return n;let o=e[0].size(this.resolveDataElementOptions(0)),a=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,a)/2}};M(de,"id","scatter"),M(de,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),M(de,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var Nr=Object.freeze({__proto__:null,BarController:re,BubbleController:le,DoughnutController:kt,LineController:ce,PieController:Le,PolarAreaController:$t,RadarController:he,ScatterController:de});function Wt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var ws=class i{constructor(t){M(this,"options");this.options=t||{}}static override(t){Object.assign(i.prototype,t)}init(){}formats(){return Wt()}parse(){return Wt()}format(){return Wt()}add(){return Wt()}diff(){return Wt()}startOf(){return Wt()}endOf(){return Wt()}},Hr={_date:ws};function jr(i,t,e,s){let{controller:n,data:o,_sorted:a}=i,r=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(r&&t===r.axis&&t!=="r"&&a&&o.length){let c=r._reversePixels?ln:ct;if(s){if(n._sharedOptions){let h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){let u=c(o,t,e-d),f=c(o,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{let h=c(o,t,e);if(l){let{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(p=>!A(p[d.axis]));h.lo-=Math.max(0,f);let g=u.slice(h.hi).findIndex(p=>!A(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function je(i,t,e,s,n){let o=i.getSortedVisibleDatasetMetas(),a=e[t];for(let r=0,l=o.length;r<l;++r){let{index:c,data:h}=o[r],{lo:d,hi:u}=jr(o[r],t,a,n);for(let f=d;f<=u;++f){let g=h[f];g.skip||s(g,c,f)}}}function $r(i){let t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){let o=t?Math.abs(s.x-n.x):0,a=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}function ps(i,t,e,s,n){let o=[];return!n&&!i.isPointInArea(t)||je(i,e,t,function(r,l,c){!n&&!ht(r,i.chartArea,0)||r.inRange(t.x,t.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function Yr(i,t,e,s){let n=[];function o(a,r,l){let{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=Hi(a,{x:t.x,y:t.y});ie(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}return je(i,e,t,o),n}function Ur(i,t,e,s,n,o){let a=[],r=$r(e),l=Number.POSITIVE_INFINITY;function c(h,d,u){let f=h.inRange(t.x,t.y,n);if(s&&!f)return;let g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;let m=r(t,g);m<l?(a=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&a.push({element:h,datasetIndex:d,index:u})}return je(i,e,t,c),a}function ms(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Yr(i,t,e,n):Ur(i,t,e,s,n,o)}function Vn(i,t,e,s,n){let o=[],a=e==="x"?"inXRange":"inYRange",r=!1;return je(i,e,t,(l,c,h)=>{l[a]&&l[a](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(t.x,t.y,n))}),s&&!r?[]:o}var Xr={evaluateInteractionItems:je,modes:{index(i,t,e,s){let n=Tt(t,i),o=e.axis||"x",a=e.includeInvisible||!1,r=e.intersect?ps(i,n,o,s,a):ms(i,n,o,!1,s,a),l=[];return r.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{let h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){let n=Tt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1,r=e.intersect?ps(i,n,o,s,a):ms(i,n,o,!1,s,a);if(r.length>0){let l=r[0].datasetIndex,c=i.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point(i,t,e,s){let n=Tt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return ps(i,n,o,s,a)},nearest(i,t,e,s){let n=Tt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return ms(i,n,o,e.intersect,s,a)},x(i,t,e,s){let n=Tt(t,i);return Vn(i,n,"x",e.intersect,s)},y(i,t,e,s){let n=Tt(t,i);return Vn(i,n,"y",e.intersect,s)}}},Lo=["left","top","right","bottom"];function Oe(i,t){return i.filter(e=>e.pos===t)}function Wn(i,t){return i.filter(e=>Lo.indexOf(e.pos)===-1&&e.box.axis===t)}function Ce(i,t){return i.sort((e,s)=>{let n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Kr(i){let t=[],e,s,n,o,a,r;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:a,stackWeight:r=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return t}function qr(i){let t={};for(let e of i){let{stack:s,pos:n,stackWeight:o}=e;if(!s||!Lo.includes(n))continue;let a=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return t}function Gr(i,t){let e=qr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t,o,a,r;for(o=0,a=i.length;o<a;++o){r=i[o];let{fullSize:l}=r.box,c=e[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&t.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&t.availableHeight)}return e}function Zr(i){let t=Kr(i),e=Ce(t.filter(c=>c.box.fullSize),!0),s=Ce(Oe(t,"left"),!0),n=Ce(Oe(t,"right")),o=Ce(Oe(t,"top"),!0),a=Ce(Oe(t,"bottom")),r=Wn(t,"x"),l=Wn(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:Oe(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}function Nn(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Ro(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Jr(i,t,e,s){let{pos:n,box:o}=e,a=i.maxPadding;if(!T(n)){e.size&&(i[n]-=e.size);let d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Ro(a,o.getPadding());let r=Math.max(0,t.outerWidth-Nn(a,i,"left","right")),l=Math.max(0,t.outerHeight-Nn(a,i,"top","bottom")),c=r!==i.w,h=l!==i.h;return i.w=r,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function Qr(i){let t=i.maxPadding;function e(s){let n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function tl(i,t){let e=t.maxPadding;function s(n){let o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(t[a],e[a])}),o}return s(i?["left","right"]:["top","bottom"])}function Re(i,t,e,s){let n=[],o,a,r,l,c,h;for(o=0,a=i.length,c=0;o<a;++o){r=i[o],l=r.box,l.update(r.width||t.w,r.height||t.h,tl(r.horizontal,t));let{same:d,other:u}=Jr(t,e,r,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(r)}return c&&Re(n,t,e,s)||h}function fi(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Hn(i,t,e,s){let n=e.padding,{x:o,y:a}=t;for(let r of i){let l=r.box,c=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){let d=t.w*h,u=c.size||l.height;te(c.start)&&(a=c.start),l.fullSize?fi(l,n.left,a,e.outerWidth-n.right-n.left,u):fi(l,t.left+c.placed,a,d,u),c.start=a,c.placed+=d,a=l.bottom}else{let d=t.h*h,u=c.size||l.width;te(c.start)&&(o=c.start),l.fullSize?fi(l,o,n.top,u,e.outerHeight-n.bottom-n.top):fi(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=a}var Z={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){let e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;let n=q(i.options.layout.padding),o=Math.max(t-n.width,0),a=Math.max(e-n.height,0),r=Zr(i.boxes),l=r.vertical,c=r.horizontal;E(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});let h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);Ro(u,q(s));let f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),g=Gr(l.concat(c),d);Re(r.fullSize,f,d,g),Re(l,f,d,g),Re(c,f,d,g)&&Re(l,f,d,g),Qr(f),Hn(r.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Hn(r.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},E(r.chartArea,p=>{let m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}},Mi=class{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}},Ss=class extends Mi{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}},_i="$chartjs",el={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},jn=i=>i===null||i==="";function il(i,t){let e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[_i]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",jn(n)){let o=os(i,"width");o!==void 0&&(i.width=o)}if(jn(s))if(i.style.height==="")i.height=i.width/(t||2);else{let o=os(i,"height");o!==void 0&&(i.height=o)}return i}var Eo=Sn?{passive:!0}:!1;function sl(i,t,e){i&&i.addEventListener(t,e,Eo)}function nl(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,Eo)}function ol(i,t){let e=el[i.type]||i.type,{x:s,y:n}=Tt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function ki(i,t){for(let e of i)if(e===t||e.contains(t))return!0}function al(i,t,e){let s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(let r of o)a=a||ki(r.addedNodes,s),a=a&&!ki(r.removedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function rl(i,t,e){let s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(let r of o)a=a||ki(r.removedNodes,s),a=a&&!ki(r.addedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}var We=new Map,$n=0;function Io(){let i=window.devicePixelRatio;i!==$n&&($n=i,We.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function ll(i,t){We.size||window.addEventListener("resize",Io),We.set(i,t)}function cl(i){We.delete(i),We.size||window.removeEventListener("resize",Io)}function hl(i,t,e){let s=i.canvas,n=s&&di(s);if(!n)return;let o=Ui((r,l)=>{let c=n.clientWidth;e(r,l),c<n.clientWidth&&e()},window),a=new ResizeObserver(r=>{let l=r[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return a.observe(n),ll(i,o),a}function bs(i,t,e){e&&e.disconnect(),t==="resize"&&cl(i)}function dl(i,t,e){let s=i.canvas,n=Ui(o=>{i.ctx!==null&&e(ol(o,i))},i);return sl(s,t,n),n}var Ps=class extends Mi{acquireContext(t,e){let s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(il(t,e),s):null}releaseContext(t){let e=t.canvas;if(!e[_i])return!1;let s=e[_i].initial;["height","width"].forEach(o=>{let a=s[o];A(a)?e.removeAttribute(o):e.setAttribute(o,a)});let n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[_i],!0}addEventListener(t,e,s){this.removeEventListener(t,e);let n=t.$proxies||(t.$proxies={}),a={attach:al,detach:rl,resize:hl}[e]||dl;n[e]=a(t,e,s)}removeEventListener(t,e){let s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:bs,detach:bs,resize:bs}[e]||nl)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return wn(t,e,s,n)}isAttached(t){let e=t&&di(t);return!!(e&&e.isConnected)}};function ul(i){return!hi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Ss:Ps}var st=class{constructor(){M(this,"x");M(this,"y");M(this,"active",!1);M(this,"options");M(this,"$animations")}tooltipPosition(t){let{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Bt(this.x)&&Bt(this.y)}getProps(t,e){let s=this.$animations;if(!e||!s)return this;let n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}};M(st,"defaults",{}),M(st,"defaultRoutes");function fl(i,t){let e=i.options.ticks,s=gl(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?ml(t):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return bl(t,c,o,a/n),c;let h=pl(o,t,n);if(a>0){let d,u,f=a>1?Math.round((l-r)/(a-1)):null;for(gi(t,c,h,A(f)?0:r-f,r),d=0,u=a-1;d<u;d++)gi(t,c,h,o[d],o[d+1]);return gi(t,c,h,l,A(f)?t.length:l+f),c}return gi(t,c,h),c}function gl(i){let t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function pl(i,t,e){let s=xl(i),n=t.length/e;if(!s)return Math.max(n,1);let o=on(s);for(let a=0,r=o.length-1;a<r;a++){let l=o[a];if(l>n)return l}return Math.max(n,1)}function ml(i){let t=[],e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function bl(i,t,e,s){let n=0,o=e[0],a;for(s=Math.ceil(s),a=0;a<i.length;a++)a===o&&(t.push(i[a]),n++,o=e[n*s])}function gi(i,t,e,s,n){let o=D(s,0),a=Math.min(D(n,i.length),i.length),r=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)r++,h=Math.round(o+r*e);for(c=Math.max(o,0);c<a;c++)c===h&&(t.push(i[c]),r++,h=Math.round(o+r*e))}function xl(i){let t=i.length,e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}var _l=i=>i==="left"?"right":i==="right"?"left":i,Yn=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Un=(i,t)=>Math.min(t||i,i);function Xn(i,t){let e=[],s=i.length/t,n=i.length,o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function yl(i,t,e){let s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,a=i._endPixel,r=1e-6,l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,a-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-r||l>a+r)))return l}function vl(i,t){E(i,e=>{let s=e.gc,n=s.length/2,o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Ae(i){return i.drawTicks?i.tickLength:0}function Kn(i,t){if(!i.display)return 0;let e=$(i.font,t),s=q(i.padding);return(z(i.text)?i.text.length:1)*e.lineHeight+s.height}function Ml(i,t){return yt(i,{scale:t,type:"scale"})}function kl(i,t,e){return yt(i,{tick:e,index:t,type:"tick"})}function wl(i,t,e){let s=oi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=_l(s)),s}function Sl(i,t,e,s){let{top:n,left:o,bottom:a,right:r,chart:l}=i,{chartArea:c,scales:h}=l,d=0,u,f,g,p=a-n,m=r-o;if(i.isHorizontal()){if(f=K(s,o,r),T(e)){let b=Object.keys(e)[0],x=e[b];g=h[b].getPixelForValue(x)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=Yn(i,e,t);u=r-o}else{if(T(e)){let b=Object.keys(e)[0],x=e[b];f=h[b].getPixelForValue(x)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=Yn(i,e,t);g=K(s,a,n),d=e==="left"?-H:H}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}var Ut=class i extends st{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=J(t,Number.POSITIVE_INFINITY),e=J(e,Number.NEGATIVE_INFINITY),s=J(s,Number.POSITIVE_INFINITY),n=J(n,Number.NEGATIVE_INFINITY),{min:J(t,s),max:J(e,n),minDefined:N(t),maxDefined:N(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),a;if(n&&o)return{min:e,max:s};let r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,t),n||(e=Math.min(e,a.min)),o||(s=Math.max(s,a.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:J(e,J(s,e)),max:J(s,J(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){F(this.options.beforeUpdate,[this])}update(t,e,s){let{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=xn(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=r<this.ticks.length;this._convertTicksToLabels(l?Xn(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=fl(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){F(this.options.afterUpdate,[this])}beforeSetDimensions(){F(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){F(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),F(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){F(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e=this.options.ticks,s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=F(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){F(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){F(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t=this.options,e=t.ticks,s=Un(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation,a=n,r,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=Y(this.chart.width-d,0,this.maxWidth);r=t.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(t.offset?.5:1)),l=this.maxHeight-Ae(t.grid)-e.padding-Kn(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),a=si(Math.min(Math.asin(Y((h.highest.height+6)/r,-1,1)),Math.asin(Y(l/c,-1,1))-Math.asin(Y(u/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){F(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){F(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){let l=Kn(n,e.options.font);if(r?(t.width=this.maxWidth,t.height=Ae(o)+l):(t.height=this.maxHeight,t.width=Ae(o)+l),s.display&&this.ticks.length){let{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=ot(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(r){let b=s.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{let b=s.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){let{ticks:{align:o,padding:a},position:r}=this.options,l=this.labelRotation!==0,c=r!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1),u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){F(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)A(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,s=this.ticks;e<s.length&&(s=Xn(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){let{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(e/Un(e,s)),c=0,h=0,d,u,f,g,p,m,b,x,v,y,_;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},x=p.lineHeight,v=y=0,!A(g)&&!z(g))v=Me(n,b.data,b.gc,v,g),y=x;else if(z(g))for(u=0,f=g.length;u<f;++u)_=g[u],!A(_)&&!z(_)&&(v=Me(n,b.data,b.gc,v,_),y+=x);a.push(v),r.push(y),c=Math.max(v,c),h=Math.max(y,h)}vl(o,e);let k=a.indexOf(c),w=r.indexOf(h),S=P=>({width:a[P]||0,height:r[P]||0});return{first:S(0),last:S(e-1),widest:S(k),highest:S(w),widths:a,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return rn(this._alignToPixels?Ot(this.chart,e,0):e)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let s=e[t];return s.$context||(s.$context=kl(this.getContext(),t,s))}return this.$context||(this.$context=Ml(this.chart.getContext(),this))}_tickSize(){let t=this.options.ticks,e=ot(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),a=t.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){let t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Ae(o),f=[],g=r.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(W){return Ot(s,W,p)},x,v,y,_,k,w,S,P,O,C,L,U;if(a==="top")x=b(this.bottom),w=this.bottom-u,P=x-m,C=b(t.top)+m,U=t.bottom;else if(a==="bottom")x=b(this.top),C=t.top,U=b(t.bottom)-m,w=x+m,P=this.top+u;else if(a==="left")x=b(this.right),k=this.right-u,S=x-m,O=b(t.left)+m,L=t.right;else if(a==="right")x=b(this.left),O=t.left,L=b(t.right)-m,k=x+m,S=this.left+u;else if(e==="x"){if(a==="center")x=b((t.top+t.bottom)/2+.5);else if(T(a)){let W=Object.keys(a)[0],j=a[W];x=b(this.chart.scales[W].getPixelForValue(j))}C=t.top,U=t.bottom,w=x+m,P=w+u}else if(e==="y"){if(a==="center")x=b((t.left+t.right)/2);else if(T(a)){let W=Object.keys(a)[0],j=a[W];x=b(this.chart.scales[W].getPixelForValue(j))}k=x-m,S=k-u,O=t.left,L=t.right}let et=D(n.ticks.maxTicksLimit,d),I=Math.max(1,Math.ceil(d/et));for(v=0;v<d;v+=I){let W=this.getContext(v),j=o.setContext(W),rt=r.setContext(W),G=j.lineWidth,Xt=j.color,$e=rt.dash||[],Kt=rt.dashOffset,me=j.tickWidth,Rt=j.tickColor,be=j.tickBorderDash||[],Et=j.tickBorderDashOffset;y=yl(this,v,l),y!==void 0&&(_=Ot(s,y,G),c?k=S=O=L=_:w=P=C=U=_,f.push({tx1:k,ty1:w,tx2:S,ty2:P,x1:O,y1:C,x2:L,y2:U,width:G,color:Xt,borderDash:$e,borderDashOffset:Kt,tickWidth:me,tickColor:Rt,tickBorderDash:be,tickBorderDashOffset:Et}))}return this._ticksLength=d,this._borderValue=x,f}_computeLabelItems(t){let e=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Ae(s.grid),f=u+h,g=d?-h:f,p=-ot(this.labelRotation),m=[],b,x,v,y,_,k,w,S,P,O,C,L,U="middle";if(n==="top")k=this.bottom-g,w=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+g,w=this._getXAxisLabelAlignment();else if(n==="left"){let I=this._getYAxisLabelAlignment(u);w=I.textAlign,_=I.x}else if(n==="right"){let I=this._getYAxisLabelAlignment(u);w=I.textAlign,_=I.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+f;else if(T(n)){let I=Object.keys(n)[0],W=n[I];k=this.chart.scales[I].getPixelForValue(W)+f}w=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")_=(t.left+t.right)/2-f;else if(T(n)){let I=Object.keys(n)[0],W=n[I];_=this.chart.scales[I].getPixelForValue(W)}w=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?U="top":l==="end"&&(U="bottom"));let et=this._getLabelSizes();for(b=0,x=r.length;b<x;++b){v=r[b],y=v.label;let I=o.setContext(this.getContext(b));S=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),O=P.lineHeight,C=z(y)?y.length:1;let W=C/2,j=I.color,rt=I.textStrokeColor,G=I.textStrokeWidth,Xt=w;a?(_=S,w==="inner"&&(b===x-1?Xt=this.options.reverse?"left":"right":b===0?Xt=this.options.reverse?"right":"left":Xt="center"),n==="top"?c==="near"||p!==0?L=-C*O+O/2:c==="center"?L=-et.highest.height/2-W*O+O:L=-et.highest.height+O/2:c==="near"||p!==0?L=O/2:c==="center"?L=et.highest.height/2-W*O:L=et.highest.height-C*O,d&&(L*=-1),p!==0&&!I.showLabelBackdrop&&(_+=O/2*Math.sin(p))):(k=S,L=(1-C)*O/2);let $e;if(I.showLabelBackdrop){let Kt=q(I.backdropPadding),me=et.heights[b],Rt=et.widths[b],be=L-Kt.top,Et=0-Kt.left;switch(U){case"middle":be-=me/2;break;case"bottom":be-=me;break}switch(w){case"center":Et-=Rt/2;break;case"right":Et-=Rt;break;case"inner":b===x-1?Et-=Rt:b>0&&(Et-=Rt/2);break}$e={left:Et,top:be,width:Rt+Kt.width,height:me+Kt.height,color:I.backdropColor}}m.push({label:y,font:P,textOffset:L,options:{rotation:p,color:j,strokeColor:rt,strokeWidth:G,textAlign:Xt,textBaseline:U,translation:[_,k],backdrop:$e}})}return m}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-ot(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){let{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,a=this._getLabelSizes(),r=t+o,l=a.widest.width,c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:a}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,a),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){let e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),o,a,r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,a=n.length;o<a;++o){let l=n[o];e.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;let r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue,c,h,d,u;this.isHorizontal()?(c=Ot(t,this.left,a)-a/2,h=Ot(t,this.right,r)+r/2,d=u=l):(d=Ot(t,this.top,a)-a/2,u=Ot(t,this.bottom,r)+r/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;let s=this.ctx,n=this._computeLabelArea();n&&Se(s,n);let o=this.getLabelItems(t);for(let a of o){let r=a.options,l=a.font,c=a.label,h=a.textOffset;Ct(s,c,0,h,l,r)}n&&Pe(s)}drawTitle(){let{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;let o=$(s.font),a=q(s.padding),r=s.align,l=o.lineHeight/2;e==="bottom"||e==="center"||T(e)?(l+=a.bottom,z(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;let{titleX:c,titleY:h,maxWidth:d,rotation:u}=Sl(this,l,e,r);Ct(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:wl(r,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==i.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){let e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[],o,a;for(o=0,a=e.length;o<a;++o){let r=e[o];r[s]===this.id&&(!t||r.type===t)&&n.push(r)}return n}_resolveTickFontOptions(t){let e=this.options.ticks.setContext(this.getContext(t));return $(e.font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}},ge=class{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e=Object.getPrototypeOf(t),s;Ol(e)&&(s=this.register(e));let n=this.items,o=t.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Pl(t,a,s),this.override&&V.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){let e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in V[n]&&(delete V[n][s],this.override&&delete Dt[s])}};function Pl(i,t,e){let s=Zt(Object.create(null),[e?V.get(e):{},V.get(t),i.defaults]);V.set(t,s),i.defaultRoutes&&Dl(t,i.defaultRoutes),i.descriptors&&V.describe(t,i.descriptors)}function Dl(i,t){Object.keys(t).forEach(e=>{let s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),a=t[e].split("."),r=a.pop(),l=a.join(".");V.route(o,n,l,r)})}function Ol(i){return"id"in i&&"defaults"in i}var Ds=class{constructor(){this.controllers=new ge(it,"datasets",!0),this.elements=new ge(st,"elements"),this.plugins=new ge(Object,"plugins"),this.scales=new ge(Ut,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{let o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):E(n,a=>{let r=s||this._getRegistryForType(a);this._exec(t,r,a)})})}_exec(t,e,s){let n=ii(t);F(s["before"+n],[],s),e[t](s),F(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){let n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}},gt=new Ds,Os=class{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let o=n?this._descriptors(t).filter(n):this._descriptors(t),a=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),a}_notify(t,e,s,n){n=n||{};for(let o of t){let a=o.plugin,r=a[s],l=[e,n,o.options];if(F(r,l,a)===!1&&n.cancelable)return!1}return!0}invalidate(){A(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=Cl(s);return n===!1&&!e?[]:Tl(t,o,n,e)}_notifyStateChanges(t){let e=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}};function Cl(i){let t={},e=[],s=Object.keys(gt.plugins.items);for(let o=0;o<s.length;o++)e.push(gt.getPlugin(s[o]));let n=i.plugins||[];for(let o=0;o<n.length;o++){let a=n[o];e.indexOf(a)===-1&&(e.push(a),t[a.id]=!0)}return{plugins:e,localIds:t}}function Al(i,t){return!t&&i===!1?null:i===!0?{}:i}function Tl(i,{plugins:t,localIds:e},s,n){let o=[],a=i.getContext();for(let r of t){let l=r.id,c=Al(s[l],n);c!==null&&o.push({plugin:r,options:Ll(i.config,{plugin:r,local:e[l]},c,a)})}return o}function Ll(i,{plugin:t,local:e},s,n){let o=i.pluginScopeKeys(t),a=i.getOptionScopes(s,o);return e&&t.defaults&&a.push(t.defaults),i.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Cs(i,t){let e=V.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Rl(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function El(i,t){return i===t?"_index_":"_value_"}function qn(i){if(i==="x"||i==="y"||i==="r")return i}function Il(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function As(i,...t){if(qn(i))return i;for(let e of t){let s=e.axis||Il(e.position)||i.length>1&&qn(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Gn(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Fl(i,t){if(t.data&&t.data.datasets){let e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Gn(i,"x",e[0])||Gn(i,"y",e[0])}return{}}function zl(i,t){let e=Dt[i.type]||{scales:{}},s=t.scales||{},n=Cs(i.type,t),o=Object.create(null);return Object.keys(s).forEach(a=>{let r=s[a];if(!T(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);let l=As(a,r,Fl(a,i),V.scales[r.type]),c=El(l,n),h=e.scales||{};o[a]=Qt(Object.create(null),[{axis:l},r,h[l],h[c]])}),i.data.datasets.forEach(a=>{let r=a.type||i.type,l=a.indexAxis||Cs(r,t),h=(Dt[r]||{}).scales||{};Object.keys(h).forEach(d=>{let u=Rl(d,l),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),Qt(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{let r=o[a];Qt(r,[V.scales[r.type],V.scale])}),o}function Fo(i){let t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=zl(i,t)}function zo(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Bl(i){return i=i||{},i.data=zo(i.data),Fo(i),i}var Zn=new Map,Bo=new Set;function pi(i,t){let e=Zn.get(i);return e||(e=t(),Zn.set(i,e),Bo.add(e)),e}var Te=(i,t,e)=>{let s=_t(t,e);s!==void 0&&i.add(s)},Ts=class{constructor(t){this._config=Bl(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=zo(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),Fo(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return pi(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return pi(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return pi(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,s=this.type;return pi(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let s=this._scopeCache,n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){let{options:n,type:o}=this,a=this._cachedScopes(t,s),r=a.get(e);if(r)return r;let l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>Te(l,t,d))),h.forEach(d=>Te(l,n,d)),h.forEach(d=>Te(l,Dt[o]||{},d)),h.forEach(d=>Te(l,V,d)),h.forEach(d=>Te(l,ai,d))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Bo.has(e)&&a.set(e,c),c}chartOptionScopes(){let{options:t,type:e}=this;return[t,Dt[e]||{},V.datasets[e]||{},{type:e},V,ai]}resolveNamedOptions(t,e,s,n=[""]){let o={$shared:!0},{resolver:a,subPrefixes:r}=Jn(this._resolverCache,t,n),l=a;if(Wl(a,e)){o.$shared=!1,s=bt(s)?s():s;let c=this.createResolver(t,s,r);l=zt(a,s,c)}for(let c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){let{resolver:o}=Jn(this._resolverCache,t,s);return T(e)?zt(o,e,void 0,n):o}};function Jn(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));let n=e.join(),o=s.get(n);return o||(o={resolver:ci(t,e),subPrefixes:e.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}var Vl=i=>T(i)&&Object.getOwnPropertyNames(i).some(t=>bt(i[t]));function Wl(i,t){let{isScriptable:e,isIndexable:s}=ts(i);for(let n of t){let o=e(n),a=s(n),r=(a||o)&&i[n];if(o&&(bt(r)||Vl(r))||a&&z(r))return!0}return!1}var Nl="4.5.0",Hl=["top","bottom","left","right","chartArea"];function Qn(i,t){return i==="top"||i==="bottom"||Hl.indexOf(i)===-1&&t==="x"}function to(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function eo(i){let t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),F(e&&e.onComplete,[i],t)}function jl(i){let t=i.chart,e=t.options.animation;F(e&&e.onProgress,[i],t)}function Vo(i){return hi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}var yi={},io=i=>{let t=Vo(i);return Object.values(yi).filter(e=>e.canvas===t).pop()};function $l(i,t,e){let s=Object.keys(i);for(let n of s){let o=+n;if(o>=t){let a=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=a)}}}function Yl(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}var at=class{static register(...t){gt.add(...t),so()}static unregister(...t){gt.remove(...t),so()}constructor(t,e){let s=this.config=new Ts(e),n=Vo(t),o=io(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");let a=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||ul(n)),this.platform.updateConfig(s);let r=this.platform.acquireContext(n,a.aspectRatio),l=r&&r.canvas,c=l&&l.height,h=l&&l.width;if(this.id=tn(),this.ctx=r,this.canvas=l,this.width=h,this.height=c,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Os,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=un(d=>this.update(d),a.resizeDelay||0),this._dataChanges=[],yi[this.id]=this,!r||!l){console.error("Failed to create chart: can't acquire context from the given item");return}vt.listen(this,"complete",eo),vt.listen(this,"progress",jl),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return A(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return gt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ns(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Zi(this.canvas,this.ctx),this}stop(){return vt.stop(this),this}resize(t,e){vt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(n,t,e,o),r=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,ns(this,r,!0)&&(this.notifyPlugins("resize",{size:a}),F(s.onResize,[this,a],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){let e=this.options.scales||{};E(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){let t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((a,r)=>(a[r]=!1,a),{}),o=[];e&&(o=o.concat(Object.keys(e).map(a=>{let r=e[a],l=As(a,r),c=l==="r",h=l==="x";return{options:r,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),E(o,a=>{let r=a.options,l=r.id,c=As(l,r),h=D(r.type,a.dtype);(r.position===void 0||Qn(r.position,c)!==Qn(a.dposition))&&(r.position=a.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{let u=gt.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(r,t)}),E(n,(a,r)=>{a||delete s[r]}),E(s,a=>{Z.configure(this,a,a.options),Z.addBox(this,a)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(to("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){let t=[],e=this.data.datasets,s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){let o=e[s],a=this.getDatasetMeta(s),r=o.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(s),a=this.getDatasetMeta(s)),a.type=r,a.indexAxis=o.indexAxis||Cs(r,this.options),a.order=o.order||0,a.index=s,a.label=""+o.label,a.visible=this.isDatasetVisible(s),a.controller)a.controller.updateIndex(s),a.controller.linkScales();else{let l=gt.getController(r),{datasetElementType:c,dataElementType:h}=V.datasets[r];Object.assign(l,{dataElementType:gt.getElement(h),datasetElementType:c&&gt.getElement(c)}),a.controller=new l(this,s),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){E(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let c=0,h=this.data.datasets.length;c<h;c++){let{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),a=Math.max(+d.getMaxOverflow(),a)}a=this._minPadding=s.layout.autoPadding?a:0,this._updateLayout(a),n||E(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(to("z","_idx"));let{_active:r,_lastEvent:l}=this;l?this._eventHandler(l,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){E(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Bi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(let{method:s,start:n,count:o}of e){let a=s==="_removeElements"?-o:o;$l(t,n,a)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,s=o=>new Set(t.filter(a=>a[0]===o).map((a,r)=>r+","+a.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Bi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);let e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],E(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,bt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(vt.has(this)?this.attached&&!vt.running(this)&&vt.start(this):(this.draw(),eo({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e=this._sortedMetasets,s=[],n,o;for(n=0,o=e.length;n<o;++n){let a=e[n];(!t||a.visible)&&s.push(a)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=hs(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&Se(e,n),t.controller.draw(),n&&Pe(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return ht(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){let o=Xr.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){let e=this.data.datasets[t],s=this._metasets,n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=yt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){let s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){let n=s?"show":"hide",o=this.getDatasetMeta(t),a=o.controller._resolveAnimations(void 0,n);te(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),a.update(o,{visible:s}),this.update(r=>r.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),vt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Zi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete yi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,s=(o,a)=>{e.addEventListener(this,o,a),t[o]=a},n=(o,a,r)=>{o.offsetX=a,o.offsetY=r,this._eventHandler(o)};E(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)},a,r=()=>{n("attach",r),this.attached=!0,this.resize(),s("resize",o),s("detach",a)};a=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",r)},e.isAttached(this.canvas)?r():a()}unbindEvents(){E(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},E(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){let n=s?"set":"remove",o,a,r,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),r=0,l=t.length;r<l;++r){a=t[r];let c=a&&this.getDatasetMeta(a.datasetIndex).controller;c&&c[n+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],s=t.map(({datasetIndex:o,index:a})=>{let r=this.getDatasetMeta(o);if(!r)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:r.data[a],index:a}});!ke(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){let n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),a=o(e,t),r=s?t:o(t,e);a.length&&this.updateHoverStyle(a,n.mode,!1),r.length&&n.mode&&this.updateHoverStyle(r,n.mode,!0)}_eventHandler(t,e){let s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=a=>(a.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;let o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){let{_active:n=[],options:o}=this,a=e,r=this._getActiveElements(t,n,s,a),l=nn(t),c=Yl(t,this._lastEvent,s,l);s&&(this._lastEvent=null,F(o.onHover,[t,r,this],this),l&&F(o.onClick,[t,r,this],this));let h=!ke(r,n);return(h||e)&&(this._active=r,this._updateHoverStyles(r,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;let o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}};M(at,"defaults",V),M(at,"instances",yi),M(at,"overrides",Dt),M(at,"registry",gt),M(at,"version",Nl),M(at,"getChart",io);function so(){return E(at.instances,i=>i._plugins.invalidate())}function Ul(i,t,e){let{startAngle:s,x:n,y:o,outerRadius:a,innerRadius:r,options:l}=t,{borderWidth:c,borderJoinStyle:h}=l,d=Math.min(c/a,X(s-e));if(i.beginPath(),i.arc(n,o,a-c/2,s+d/2,e-d/2),r>0){let u=Math.min(c/r,X(s-e));i.arc(n,o,r+c/2,e-u/2,s+u/2,!0)}else{let u=Math.min(c/2,a*X(s-e));if(h==="round")i.arc(n,o,u,e-R/2,s+R/2,!0);else if(h==="bevel"){let f=2*u*u,g=-f*Math.cos(e+R/2)+n,p=-f*Math.sin(e+R/2)+o,m=f*Math.cos(s+R/2)+n,b=f*Math.sin(s+R/2)+o;i.lineTo(g,p),i.lineTo(m,b)}}i.closePath(),i.moveTo(0,0),i.rect(0,0,i.canvas.width,i.canvas.height),i.clip("evenodd")}function Xl(i,t,e){let{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=t,c=n/r;i.beginPath(),i.arc(o,a,r,s-c,e+c),l>n?(c=n/l,i.arc(o,a,l,e+c,s-c,!0)):i.arc(o,a,n,e+H,s-H),i.closePath(),i.clip()}function Kl(i){return li(i,["outerStart","outerEnd","innerStart","innerEnd"])}function ql(i,t,e,s){let n=Kl(i.options.borderRadius),o=(e-t)/2,a=Math.min(o,s*t/2),r=l=>{let c=(e-Math.min(o,l))*s/2;return Y(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:Y(n.innerStart,0,a),innerEnd:Y(n.innerEnd,0,a)}}function ae(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function wi(i,t,e,s,n,o){let{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0,f=0,g=n-l;if(s){let I=h>0?h-s:0,W=d>0?d-s:0,j=(I+W)/2,rt=j!==0?g*j/(j+s):g;f=(g-rt)/2}let p=Math.max(.001,g*d-e/R)/d,m=(g-p)/2,b=l+m+f,x=n-m-f,{outerStart:v,outerEnd:y,innerStart:_,innerEnd:k}=ql(t,u,d,x-b),w=d-v,S=d-y,P=b+v/w,O=x-y/S,C=u+_,L=u+k,U=b+_/C,et=x-k/L;if(i.beginPath(),o){let I=(P+O)/2;if(i.arc(a,r,d,P,I),i.arc(a,r,d,I,O),y>0){let G=ae(S,O,a,r);i.arc(G.x,G.y,y,O,x+H)}let W=ae(L,x,a,r);if(i.lineTo(W.x,W.y),k>0){let G=ae(L,et,a,r);i.arc(G.x,G.y,k,x+H,et+Math.PI)}let j=(x-k/u+(b+_/u))/2;if(i.arc(a,r,u,x-k/u,j,!0),i.arc(a,r,u,j,b+_/u,!0),_>0){let G=ae(C,U,a,r);i.arc(G.x,G.y,_,U+Math.PI,b-H)}let rt=ae(w,b,a,r);if(i.lineTo(rt.x,rt.y),v>0){let G=ae(w,P,a,r);i.arc(G.x,G.y,v,b-H,P)}}else{i.moveTo(a,r);let I=Math.cos(P)*d+a,W=Math.sin(P)*d+r;i.lineTo(I,W);let j=Math.cos(O)*d+a,rt=Math.sin(O)*d+r;i.lineTo(j,rt)}i.closePath()}function Gl(i,t,e,s,n){let{fullCircles:o,startAngle:a,circumference:r}=t,l=t.endAngle;if(o){wi(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(r)||(l=a+(r%B||B))}return wi(i,t,e,s,l,n),i.fill(),l}function Zl(i,t,e,s,n){let{fullCircles:o,startAngle:a,circumference:r,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u,borderRadius:f}=l,g=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,g?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let p=t.endAngle;if(o){wi(i,t,e,s,p,n);for(let m=0;m<o;++m)i.stroke();isNaN(r)||(p=a+(r%B||B))}g&&Xl(i,t,p),l.selfJoin&&p-a>=R&&f===0&&h!=="miter"&&Ul(i,t,p),o||(wi(i,t,e,s,p,n),i.stroke())}var Ht=class extends st{constructor(e){super();M(this,"circumference");M(this,"endAngle");M(this,"fullCircles");M(this,"innerRadius");M(this,"outerRadius");M(this,"pixelMargin");M(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){let o=this.getProps(["x","y"],n),{angle:a,distance:r}=Hi(o,{x:e,y:s}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),f=(this.options.spacing+this.options.borderWidth)/2,g=D(u,c-l),p=ie(a,l,c)&&l!==c,m=g>=B||p,b=ut(r,h+f,d+f);return m&&b}getCenterPoint(e){let{x:s,y:n,startAngle:o,endAngle:a,innerRadius:r,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,d=(o+a)/2,u=(r+l+h+c)/2;return{x:s+Math.cos(d)*u,y:n+Math.sin(d)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){let{options:s,circumference:n}=this,o=(s.offset||0)/4,a=(s.spacing||0)/2,r=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>B?Math.floor(n/B):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();let l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);let c=1-Math.sin(Math.min(R,n||0)),h=o*c;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,Gl(e,this,h,a,r),Zl(e,this,h,a,r),e.restore()}};M(Ht,"id","arc"),M(Ht,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),M(Ht,"defaultRoutes",{backgroundColor:"backgroundColor"}),M(Ht,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function Wo(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function Jl(i,t,e){i.lineTo(e.x,e.y)}function Ql(i){return i.stepped?mn:i.tension||i.cubicInterpolationMode==="monotone"?bn:Jl}function No(i,t,e={}){let s=i.length,{start:n=0,end:o=s-1}=e,{start:a,end:r}=t,l=Math.max(n,a),c=Math.min(o,r),h=n<a&&o<a||n>r&&o>r;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function tc(i,t,e,s){let{points:n,options:o}=t,{count:a,start:r,loop:l,ilen:c}=No(n,e,s),h=Ql(o),{move:d=!0,reverse:u}=s||{},f,g,p;for(f=0;f<=c;++f)g=n[(r+(u?c-f:f))%a],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,u,o.stepped),p=g);return l&&(g=n[(r+(u?c:0))%a],h(i,p,g,u,o.stepped)),!!l}function ec(i,t,e,s){let n=t.points,{count:o,start:a,ilen:r}=No(n,e,s),{move:l=!0,reverse:c}=s||{},h=0,d=0,u,f,g,p,m,b,x=y=>(a+(c?r-y:y))%o,v=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(f=n[x(0)],i.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[x(u)],f.skip)continue;let y=f.x,_=f.y,k=y|0;k===g?(_<p?p=_:_>m&&(m=_),h=(d*h+y)/++d):(v(),i.lineTo(y,_),g=k,d=0,p=m=_),b=_}v()}function Ls(i){let t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?ec:tc}function ic(i){return i.stepped?Pn:i.tension||i.cubicInterpolationMode==="monotone"?Dn:Pt}function sc(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),Wo(i,t.options),i.stroke(n)}function nc(i,t,e,s){let{segments:n,options:o}=t,a=Ls(t);for(let r of n)Wo(i,o,r.style),i.beginPath(),a(i,t,r,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}var oc=typeof Path2D=="function";function ac(i,t,e,s){oc&&!t.options.segment?sc(i,t,e,s):nc(i,t,e,s)}var pt=class extends st{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){let n=s.spanGaps?this._loop:this._fullLoop;kn(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Cn(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){let s=this.options,n=t[e],o=this.points,a=cs(this,{property:e,start:n,end:n});if(!a.length)return;let r=[],l=ic(s),c,h;for(c=0,h=a.length;c<h;++c){let{start:d,end:u}=a[c],f=o[d],g=o[u];if(f===g){r.push(f);continue}let p=Math.abs((n-f[e])/(g[e]-f[e])),m=l(f,g,p,s.stepped);m[e]=t[e],r.push(m)}return r.length===1?r[0]:r}pathSegment(t,e,s){return Ls(this)(t,this,e,s)}path(t,e,s){let n=this.segments,o=Ls(this),a=this._loop;e=e||0,s=s||this.points.length-e;for(let r of n)a&=o(t,this,r,{start:e,end:e+s-1});return!!a}draw(t,e,s,n){let o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),ac(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}};M(pt,"id","line"),M(pt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),M(pt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),M(pt,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function no(i,t,e,s){let n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}var ue=class extends st{constructor(e){super();M(this,"parsed");M(this,"skip");M(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){let o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(e-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return no(this,e,"x",s)}inYRange(e,s){return no(this,e,"y",s)}getCenterPoint(e){let{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);let n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){let n=this.options;this.skip||n.radius<.1||!ht(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,ri(e,n,this.x,this.y))}getRange(){let e=this.options||{};return e.radius+e.hitRadius}};M(ue,"id","point"),M(ue,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),M(ue,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Ho(i,t){let{x:e,y:s,base:n,width:o,height:a}=i.getProps(["x","y","base","width","height"],t),r,l,c,h,d;return i.horizontal?(d=a/2,r=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,r=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Lt(i,t,e,s){return i?0:Y(t,e,s)}function rc(i,t,e){let s=i.options.borderWidth,n=i.borderSkipped,o=Qi(s);return{t:Lt(n.top,o.top,0,e),r:Lt(n.right,o.right,0,t),b:Lt(n.bottom,o.bottom,0,e),l:Lt(n.left,o.left,0,t)}}function lc(i,t,e){let{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=At(n),a=Math.min(t,e),r=i.borderSkipped,l=s||T(n);return{topLeft:Lt(!l||r.top||r.left,o.topLeft,0,a),topRight:Lt(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Lt(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Lt(!l||r.bottom||r.right,o.bottomRight,0,a)}}function cc(i){let t=Ho(i),e=t.right-t.left,s=t.bottom-t.top,n=rc(i,e/2,s/2),o=lc(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function xs(i,t,e,s){let n=t===null,o=e===null,r=i&&!(n&&o)&&Ho(i,s);return r&&(n||ut(t,r.left,r.right))&&(o||ut(e,r.top,r.bottom))}function hc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function dc(i,t){i.rect(t.x,t.y,t.w,t.h)}function _s(i,t,e={}){let s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,a=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+a,radius:i.radius}}var fe=class extends st{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){let{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=cc(this),r=hc(a.radius)?ne:dc;t.save(),(a.w!==o.w||a.h!==o.h)&&(t.beginPath(),r(t,_s(a,e,o)),t.clip(),r(t,_s(o,-e,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),r(t,_s(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return xs(this,t,e,s)}inXRange(t,e){return xs(this,t,null,e)}inYRange(t,e){return xs(this,null,t,e)}getCenterPoint(t){let{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}};M(fe,"id","bar"),M(fe,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),M(fe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var uc=Object.freeze({__proto__:null,ArcElement:Ht,BarElement:fe,LineElement:pt,PointElement:ue}),Rs=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],oo=Rs.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function jo(i){return Rs[i%Rs.length]}function $o(i){return oo[i%oo.length]}function fc(i,t){return i.borderColor=jo(t),i.backgroundColor=$o(t),++t}function gc(i,t){return i.backgroundColor=i.data.map(()=>jo(t++)),t}function pc(i,t){return i.backgroundColor=i.data.map(()=>$o(t++)),t}function mc(i){let t=0;return(e,s)=>{let n=i.getDatasetMeta(s).controller;n instanceof kt?t=gc(e,t):n instanceof $t?t=pc(e,t):n&&(t=fc(e,t))}}function ao(i){let t;for(t in i)if(i[t].borderColor||i[t].backgroundColor)return!0;return!1}function bc(i){return i&&(i.borderColor||i.backgroundColor)}function xc(){return V.borderColor!=="rgba(0,0,0,0.1)"||V.backgroundColor!=="rgba(0,0,0,0.1)"}var _c={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,t,e){if(!e.enabled)return;let{data:{datasets:s},options:n}=i.config,{elements:o}=n,a=ao(s)||bc(n)||o&&ao(o)||xc();if(!e.forceOverride&&a)return;let r=mc(i);s.forEach(r)}};function yc(i,t,e,s,n){let o=n.samples||s;if(o>=e)return i.slice(t,t+e);let a=[],r=(e-2)/(o-2),l=0,c=t+e-1,h=t,d,u,f,g,p;for(a[l++]=i[h],d=0;d<o-2;d++){let m=0,b=0,x,v=Math.floor((d+1)*r)+1+t,y=Math.min(Math.floor((d+2)*r)+1,e)+t,_=y-v;for(x=v;x<y;x++)m+=i[x].x,b+=i[x].y;m/=_,b/=_;let k=Math.floor(d*r)+1+t,w=Math.min(Math.floor((d+1)*r)+1,e)+t,{x:S,y:P}=i[h];for(f=g=-1,x=k;x<w;x++)g=.5*Math.abs((S-m)*(i[x].y-P)-(S-i[x].x)*(b-P)),g>f&&(f=g,u=i[x],p=x);a[l++]=u,h=p}return a[l++]=i[c],a}function vc(i,t,e,s){let n=0,o=0,a,r,l,c,h,d,u,f,g,p,m=[],b=t+e-1,x=i[t].x,y=i[b].x-x;for(a=t;a<t+e;++a){r=i[a],l=(r.x-x)/y*s,c=r.y;let _=l|0;if(_===h)c<g?(g=c,d=a):c>p&&(p=c,u=a),n=(o*n+r.x)/++o;else{let k=a-1;if(!A(d)&&!A(u)){let w=Math.min(d,u),S=Math.max(d,u);w!==f&&w!==k&&m.push({...i[w],x:n}),S!==f&&S!==k&&m.push({...i[S],x:n})}a>0&&k!==f&&m.push(i[k]),m.push(r),h=_,o=0,g=p=c,d=u=f=a}}return m}function Yo(i){if(i._decimated){let t=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function ro(i){i.data.datasets.forEach(t=>{Yo(t)})}function Mc(i,t){let e=t.length,s=0,n,{iScale:o}=i,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=Y(ct(t,o.axis,a).lo,0,e-1)),c?n=Y(ct(t,o.axis,r).hi+1,s,e)-s:n=e-s,{start:s,count:n}}var kc={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,t,e)=>{if(!e.enabled){ro(i);return}let s=i.width;i.data.datasets.forEach((n,o)=>{let{_data:a,indexAxis:r}=n,l=i.getDatasetMeta(o),c=a||n.data;if(oe([r,i.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=i.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||i.options.parsing)return;let{start:d,count:u}=Mc(l,c),f=e.threshold||4*s;if(u<=f){Yo(n);return}A(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(e.algorithm){case"lttb":g=yc(c,d,u,s,e);break;case"min-max":g=vc(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=g})},destroy(i){ro(i)}};function wc(i,t,e){let s=i.segments,n=i.points,o=t.points,a=[];for(let r of s){let{start:l,end:c}=r;c=Di(l,c,n);let h=Es(e,n[l],n[c],r.loop);if(!t.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}let d=cs(t,h);for(let u of d){let f=Es(e,o[u.start],o[u.end],u.loop),g=ls(r,n,f);for(let p of g)a.push({source:p,target:u,start:{[e]:lo(h,f,"start",Math.max)},end:{[e]:lo(h,f,"end",Math.min)}})}}return a}function Es(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=X(n),o=X(o)),{property:i,start:n,end:o}}function Sc(i,t){let{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:a,end:r})=>{r=Di(a,r,n);let l=n[a],c=n[r];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function Di(i,t,e){for(;t>i;t--){let s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function lo(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function Uo(i,t){let e=[],s=!1;return z(i)?(s=!0,e=i):e=Sc(i,t),e.length?new pt({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function co(i){return i&&i.fill!==!1}function Pc(i,t,e){let n=i[t].fill,o=[t],a;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!N(n))return n;if(a=i[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function Dc(i,t,e){let s=Tc(i);if(T(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return N(n)&&Math.floor(n)===n?Oc(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Oc(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Cc(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:T(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Ac(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:T(i)?s=i.value:s=t.getBaseValue(),s}function Tc(i){let t=i.options,e=t.fill,s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Lc(i){let{scale:t,index:e,line:s}=i,n=[],o=s.segments,a=s.points,r=Rc(t,e);r.push(Uo({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){let c=o[l];for(let h=c.start;h<=c.end;h++)Ec(n,a[h],r)}return new pt({points:n,options:{}})}function Rc(i,t){let e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){let o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Ec(i,t,e){let s=[];for(let n=0;n<e.length;n++){let o=e[n],{first:a,last:r,point:l}=Ic(o,t,"x");if(!(!l||a&&r)){if(a)s.unshift(l);else if(i.push(l),!r)break}}i.push(...s)}function Ic(i,t,e){let s=i.interpolate(t,e);if(!s)return{};let n=s[e],o=i.segments,a=i.points,r=!1,l=!1;for(let c=0;c<o.length;c++){let h=o[c],d=a[h.start][e],u=a[h.end][e];if(ut(n,d,u)){r=n===d,l=n===u;break}}return{first:r,last:l,point:s}}var Si=class{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){let{x:n,y:o,radius:a}=this;return e=e||{start:0,end:B},t.arc(n,o,a,e.end,e.start,!0),!s.bounds}interpolate(t){let{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}};function Fc(i){let{chart:t,fill:e,line:s}=i;if(N(e))return zc(t,e);if(e==="stack")return Lc(i);if(e==="shape")return!0;let n=Bc(i);return n instanceof Si?n:Uo(n,s)}function zc(i,t){let e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function Bc(i){return(i.scale||{}).getPointPositionForValue?Wc(i):Vc(i)}function Vc(i){let{scale:t={},fill:e}=i,s=Cc(e,t);if(N(s)){let n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function Wc(i){let{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,a=Ac(e,t,o),r=[];if(s.grid.circular){let l=t.getPointPositionForValue(0,o);return new Si({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(t.getPointPositionForValue(l,a));return r}function ys(i,t,e){let s=Fc(t),{chart:n,index:o,line:a,scale:r,axis:l}=t,c=a.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},g=n.getDatasetMeta(o),p=hs(n,g);s&&a.points.length&&(Se(i,e),Nc(i,{line:a,target:s,above:u,below:f,area:e,scale:r,axis:l,clip:p}),Pe(i))}function Nc(i,t){let{line:e,target:s,above:n,below:o,area:a,scale:r,clip:l}=t,c=e._loop?"angle":t.axis;i.save();let h=o;o!==n&&(c==="x"?(ho(i,s,a.top),vs(i,{line:e,target:s,color:n,scale:r,property:c,clip:l}),i.restore(),i.save(),ho(i,s,a.bottom)):c==="y"&&(uo(i,s,a.left),vs(i,{line:e,target:s,color:o,scale:r,property:c,clip:l}),i.restore(),i.save(),uo(i,s,a.right),h=n)),vs(i,{line:e,target:s,color:h,scale:r,property:c,clip:l}),i.restore()}function ho(i,t,e){let{segments:s,points:n}=t,o=!0,a=!1;i.beginPath();for(let r of s){let{start:l,end:c}=r,h=n[l],d=n[Di(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),a=!!t.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function uo(i,t,e){let{segments:s,points:n}=t,o=!0,a=!1;i.beginPath();for(let r of s){let{start:l,end:c}=r,h=n[l],d=n[Di(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(e,h.y),i.lineTo(h.x,h.y)),a=!!t.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(e,d.y)}i.lineTo(e,t.first().y),i.closePath(),i.clip()}function vs(i,t){let{line:e,target:s,property:n,color:o,scale:a,clip:r}=t,l=wc(e,s,n);for(let{source:c,target:h,start:d,end:u}of l){let{style:{backgroundColor:f=o}={}}=c,g=s!==!0;i.save(),i.fillStyle=f,Hc(i,a,r,g&&Es(n,d,u)),i.beginPath();let p=!!e.pathSegment(i,c),m;if(g){p?i.closePath():fo(i,s,u,n);let b=!!s.pathSegment(i,h,{move:p,reverse:!0});m=p&&b,m||fo(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function Hc(i,t,e,s){let n=t.chart.chartArea,{property:o,start:a,end:r}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=a,c=n.top,h=r,d=n.bottom):(l=n.left,c=a,h=n.right,d=r),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function fo(i,t,e,s){let n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var jc={id:"filler",afterDatasetsUpdate(i,t,e){let s=(i.data.datasets||[]).length,n=[],o,a,r,l;for(a=0;a<s;++a)o=i.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof pt&&(l={visible:i.isDatasetVisible(a),index:a,fill:Dc(r,a,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],!(!l||l.fill===!1)&&(l.fill=Pc(n,a,e.propagate))},beforeDraw(i,t,e){let s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let a=n.length-1;a>=0;--a){let r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&ys(i.ctx,r,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;let s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){let o=s[n].$filler;co(o)&&ys(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){let s=t.meta.$filler;!co(s)||e.drawTime!=="beforeDatasetDraw"||ys(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},go=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},$c=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index,Pi=class extends st{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=F(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}let s=t.labels,n=$(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=go(s,o),c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){let{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r,d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{let m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*r>a)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+r}),d}_fitCols(t,e,s,n){let{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-t,d=r,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{let{itemWidth:x,itemHeight:v}=Yc(s,e,o,m,n);b>0&&f+v+2*r>h&&(d+=u+r,c.push({width:u,height:f}),g+=u+r,p++,u=f=0),l[b]={left:g,top:f,col:p,width:x,height:v},u=Math.max(u,x),f+=v+r}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,a=Vt(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=K(s,this.left+n,this.right-this.lineWidths[r]);for(let c of e)r!==c.row&&(r=c.row,l=K(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+t+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=K(s,this.top+t+n,this.bottom-this.columnSizes[r].height);for(let c of e)c.col!==r&&(r=c.col,l=K(s,this.top+t+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let t=this.ctx;Se(t,this),this._draw(),Pe(t)}}_draw(){let{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:a}=t,r=V.color,l=Vt(t.rtl,this.left,this.width),c=$(a.font),{padding:h}=a,d=c.size,u=d/2,f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;let{boxWidth:g,boxHeight:p,itemHeight:m}=go(a,d),b=function(k,w,S){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();let P=D(S.lineWidth,1);if(n.fillStyle=D(S.fillStyle,r),n.lineCap=D(S.lineCap,"butt"),n.lineDashOffset=D(S.lineDashOffset,0),n.lineJoin=D(S.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=D(S.strokeStyle,r),n.setLineDash(D(S.lineDash,[])),a.usePointStyle){let O={radius:p*Math.SQRT2/2,pointStyle:S.pointStyle,rotation:S.rotation,borderWidth:P},C=l.xPlus(k,g/2),L=w+u;Ji(n,O,C,L,a.pointStyleWidth&&g)}else{let O=w+Math.max((d-p)/2,0),C=l.leftForLtr(k,g),L=At(S.borderRadius);n.beginPath(),Object.values(L).some(U=>U!==0)?ne(n,{x:C,y:O,w:g,h:p,radius:L}):n.rect(C,O,g,p),n.fill(),P!==0&&n.stroke()}n.restore()},x=function(k,w,S){Ct(n,S.text,k,w+m/2,c,{strikethrough:S.hidden,textAlign:l.textAlign(S.textAlign)})},v=this.isHorizontal(),y=this._computeTitleHeight();v?f={x:K(o,this.left+h,this.right-s[0]),y:this.top+h+y,line:0}:f={x:this.left+h,y:K(o,this.top+y+h,this.bottom-e[0].height),line:0},as(this.ctx,t.textDirection);let _=m+h;this.legendItems.forEach((k,w)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;let S=n.measureText(k.text).width,P=l.textAlign(k.textAlign||(k.textAlign=a.textAlign)),O=g+u+S,C=f.x,L=f.y;l.setWidth(this.width),v?w>0&&C+O+h>this.right&&(L=f.y+=_,f.line++,C=f.x=K(o,this.left+h,this.right-s[f.line])):w>0&&L+_>this.bottom&&(C=f.x=C+e[f.line].width+h,f.line++,L=f.y=K(o,this.top+y+h,this.bottom-e[f.line].height));let U=l.x(C);if(b(U,L,k),C=fn(P,C+g+u,v?C+O:this.right,t.rtl),x(l.x(C),L,k),v)f.x+=O+h;else if(typeof k.text!="string"){let et=c.lineHeight;f.y+=Xo(k,et)+h}else f.y+=_}),rs(this.ctx,t.textDirection)}drawTitle(){let t=this.options,e=t.title,s=$(e.font),n=q(e.padding);if(!e.display)return;let o=Vt(t.rtl,this.left,this.width),a=this.ctx,r=e.position,l=s.size/2,c=n.top+l,h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=K(t.align,d,this.right-u);else{let g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+K(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}let f=K(r,d,d+u);a.textAlign=o.textAlign(oi(r)),a.textBaseline="middle",a.strokeStyle=e.color,a.fillStyle=e.color,a.font=s.string,Ct(a,e.text,f,h,s)}_computeTitleHeight(){let t=this.options.title,e=$(t.font),s=q(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(ut(t,this.left,this.right)&&ut(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],ut(t,n.left,n.left+n.width)&&ut(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){let e=this.options;if(!Kc(t.type,e))return;let s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){let n=this._hoveredItem,o=$c(n,s);n&&!o&&F(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&F(e.onHover,[t,s,this],this)}else s&&F(e.onClick,[t,s,this],this)}};function Yc(i,t,e,s,n){let o=Uc(s,i,t,e),a=Xc(n,s,t.lineHeight);return{itemWidth:o,itemHeight:a}}function Uc(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),t+e.size/2+s.measureText(n).width}function Xc(i,t,e){let s=i;return typeof t.text!="string"&&(s=Xo(t,e)),s}function Xo(i,t){let e=i.text?i.text.length:0;return t*e}function Kc(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var qc={id:"legend",_element:Pi,start(i,t,e){let s=i.legend=new Pi({ctx:i.ctx,options:e,chart:i});Z.configure(i,s,e),Z.addBox(i,s)},stop(i){Z.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){let s=i.legend;Z.configure(i,s,e),s.options=e},afterUpdate(i){let t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){let s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){let t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{let c=l.controller.getStyle(e?0:void 0),h=q(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}},Ne=class extends st{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let n=z(s.text)?s.text.length:1;this._padding=q(s.padding);let o=n*$(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){let t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){let{top:e,left:s,bottom:n,right:o,options:a}=this,r=a.align,l=0,c,h,d;return this.isHorizontal()?(h=K(r,s,o),d=e+t,c=o-s):(a.position==="left"?(h=s+t,d=K(r,n,e),l=R*-.5):(h=o-t,d=K(r,e,n),l=R*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let s=$(e.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);Ct(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:oi(e.align),textBaseline:"middle",translation:[a,r]})}};function Gc(i,t){let e=new Ne({ctx:i.ctx,options:t,chart:i});Z.configure(i,e,t),Z.addBox(i,e),i.titleBlock=e}var Zc={id:"title",_element:Ne,start(i,t,e){Gc(i,e)},stop(i){let t=i.titleBlock;Z.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){let s=i.titleBlock;Z.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},mi=new WeakMap,Jc={id:"subtitle",start(i,t,e){let s=new Ne({ctx:i.ctx,options:e,chart:i});Z.configure(i,s,e),Z.addBox(i,s),mi.set(i,s)},stop(i){Z.removeBox(i,mi.get(i)),mi.delete(i)},beforeUpdate(i,t,e){let s=mi.get(i);Z.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Ee={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){let r=i[t].element;if(r&&r.hasValue()){let l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,a,r;for(o=0,a=i.length;o<a;++o){let l=i[o].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=ti(t,c);h<n&&(n=h,r=l)}}if(r){let l=r.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function ft(i,t){return t&&(z(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function Mt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Qc(i,t){let{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:i,label:a,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function po(i,t){let e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:a,boxHeight:r}=t,l=$(t.bodyFont),c=$(t.titleFont),h=$(t.footerFont),d=o.length,u=n.length,f=s.length,g=q(t.padding),p=g.height,m=0,b=s.reduce((y,_)=>y+_.before.length+_.lines.length+_.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){let y=t.displayColors?Math.max(r,l.lineHeight):l.lineHeight;p+=f*y+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let x=0,v=function(y){m=Math.max(m,e.measureText(y).width+x)};return e.save(),e.font=c.string,E(i.title,v),e.font=l.string,E(i.beforeBody.concat(i.afterBody),v),x=t.displayColors?a+2+t.boxPadding:0,E(s,y=>{E(y.before,v),E(y.lines,v),E(y.after,v)}),x=0,e.font=h.string,E(i.footer,v),e.restore(),m+=g.width,{width:m,height:p}}function th(i,t){let{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function eh(i,t,e,s){let{x:n,width:o}=s,a=e.caretSize+e.caretPadding;if(i==="left"&&n+o+a>t.width||i==="right"&&n-o-a<0)return!0}function ih(i,t,e,s){let{x:n,width:o}=e,{width:a,chartArea:{left:r,right:l}}=i,c="center";return s==="center"?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),eh(c,i,t,e)&&(c="center"),c}function mo(i,t,e){let s=e.yAlign||t.yAlign||th(i,e);return{xAlign:e.xAlign||t.xAlign||ih(i,t,e,s),yAlign:s}}function sh(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function nh(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function bo(i,t,e,s){let{caretSize:n,caretPadding:o,cornerRadius:a}=i,{xAlign:r,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=At(a),g=sh(t,r),p=nh(t,l,c);return l==="center"?r==="left"?g+=c:r==="right"&&(g-=c):r==="left"?g-=Math.max(h,u)+n:r==="right"&&(g+=Math.max(d,f)+n),{x:Y(g,0,s.width-t.width),y:Y(p,0,s.height-t.height)}}function bi(i,t,e){let s=q(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function xo(i){return ft([],Mt(i))}function oh(i,t,e){return yt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function _o(i,t){let e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}var Ko={beforeTitle:dt,title(i){if(i.length>0){let t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:dt,beforeBody:dt,beforeLabel:dt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");let e=i.formattedValue;return A(e)||(t+=e),t},labelColor(i){let e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){let e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:dt,afterBody:dt,beforeFooter:dt,footer:dt,afterFooter:dt};function Q(i,t,e,s){let n=i[t].call(e,s);return typeof n>"u"?Ko[t].call(e,s):n}var Ve=class extends st{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new vi(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=oh(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){let{callbacks:s}=e,n=Q(s,"beforeTitle",this,t),o=Q(s,"title",this,t),a=Q(s,"afterTitle",this,t),r=[];return r=ft(r,Mt(n)),r=ft(r,Mt(o)),r=ft(r,Mt(a)),r}getBeforeBody(t,e){return xo(Q(e.callbacks,"beforeBody",this,t))}getBody(t,e){let{callbacks:s}=e,n=[];return E(t,o=>{let a={before:[],lines:[],after:[]},r=_o(s,o);ft(a.before,Mt(Q(r,"beforeLabel",this,o))),ft(a.lines,Q(r,"label",this,o)),ft(a.after,Mt(Q(r,"afterLabel",this,o))),n.push(a)}),n}getAfterBody(t,e){return xo(Q(e.callbacks,"afterBody",this,t))}getFooter(t,e){let{callbacks:s}=e,n=Q(s,"beforeFooter",this,t),o=Q(s,"footer",this,t),a=Q(s,"afterFooter",this,t),r=[];return r=ft(r,Mt(n)),r=ft(r,Mt(o)),r=ft(r,Mt(a)),r}_createItems(t){let e=this._active,s=this.chart.data,n=[],o=[],a=[],r=[],l,c;for(l=0,c=e.length;l<c;++l)r.push(Qc(this.chart,e[l]));return t.filter&&(r=r.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(r=r.sort((h,d)=>t.itemSort(h,d,s))),E(r,h=>{let d=_o(t.callbacks,h);n.push(Q(d,"labelColor",this,h)),o.push(Q(d,"labelPointStyle",this,h)),a.push(Q(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=a,this.dataPoints=r,r}update(t,e){let s=this.options.setContext(this.getContext()),n=this._active,o,a=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{let r=Ee[s.position].call(this,n,this._eventPosition);a=this._createItems(s),this.title=this.getTitle(a,s),this.beforeBody=this.getBeforeBody(a,s),this.body=this.getBody(a,s),this.afterBody=this.getAfterBody(a,s),this.footer=this.getFooter(a,s);let l=this._size=po(this,s),c=Object.assign({},r,l),h=mo(this.chart,s,c),d=bo(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:r.x,caretY:r.y}}this._tooltipItems=a,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){let o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){let{xAlign:n,yAlign:o}=this,{caretSize:a,cornerRadius:r}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=At(r),{x:u,y:f}=t,{width:g,height:p}=e,m,b,x,v,y,_;return o==="center"?(y=f+p/2,n==="left"?(m=u,b=m-a,v=y+a,_=y-a):(m=u+g,b=m+a,v=y-a,_=y+a),x=m):(n==="left"?b=u+Math.max(l,h)+a:n==="right"?b=u+g-Math.max(c,d)-a:b=this.caretX,o==="top"?(v=f,y=v-a,m=b-a,x=b+a):(v=f+p,y=v+a,m=b+a,x=b-a),_=v),{x1:m,x2:b,x3:x,y1:v,y2:y,y3:_}}drawTitle(t,e,s){let n=this.title,o=n.length,a,r,l;if(o){let c=Vt(s.rtl,this.x,this.width);for(t.x=bi(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",a=$(s.titleFont),r=s.titleSpacing,e.fillStyle=s.titleColor,e.font=a.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+r,l+1===o&&(t.y+=s.titleMarginBottom-r)}}_drawColorBox(t,e,s,n,o){let a=this.labelColors[s],r=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=$(o.bodyFont),d=bi(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){let p={radius:Math.min(c,l)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,ri(t,p,m,b),t.strokeStyle=a.borderColor,t.fillStyle=a.backgroundColor,ri(t,p,m,b)}else{t.lineWidth=T(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,t.strokeStyle=a.borderColor,t.setLineDash(a.borderDash||[]),t.lineDashOffset=a.borderDashOffset||0;let p=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=At(a.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,ne(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=a.backgroundColor,t.beginPath(),ne(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=a.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){let{body:n}=this,{bodySpacing:o,bodyAlign:a,displayColors:r,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=$(s.bodyFont),u=d.lineHeight,f=0,g=Vt(s.rtl,this.x,this.width),p=function(S){e.fillText(S,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(a),b,x,v,y,_,k,w;for(e.textAlign=a,e.textBaseline="middle",e.font=d.string,t.x=bi(this,m,s),e.fillStyle=s.bodyColor,E(this.beforeBody,p),f=r&&m!=="right"?a==="center"?c/2+h:c+2+h:0,y=0,k=n.length;y<k;++y){for(b=n[y],x=this.labelTextColors[y],e.fillStyle=x,E(b.before,p),v=b.lines,r&&v.length&&(this._drawColorBox(e,t,y,g,s),u=Math.max(d.lineHeight,l)),_=0,w=v.length;_<w;++_)p(v[_]),u=d.lineHeight;E(b.after,p)}f=0,u=d.lineHeight,E(this.afterBody,p),t.y-=o}drawFooter(t,e,s){let n=this.footer,o=n.length,a,r;if(o){let l=Vt(s.rtl,this.x,this.width);for(t.x=bi(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",a=$(s.footerFont),e.fillStyle=s.footerColor,e.font=a.string,r=0;r<o;++r)e.fillText(n[r],l.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){let{xAlign:o,yAlign:a}=this,{x:r,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=At(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(r+d,l),a==="top"&&this.drawCaret(t,e,s,n),e.lineTo(r+c-u,l),e.quadraticCurveTo(r+c,l,r+c,l+u),a==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(r+c,l+h-g),e.quadraticCurveTo(r+c,l+h,r+c-g,l+h),a==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(r+f,l+h),e.quadraticCurveTo(r,l+h,r,l+h-f),a==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(r,l+d),e.quadraticCurveTo(r,l,r+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){let a=Ee[t.position].call(this,this._active,this._eventPosition);if(!a)return;let r=this._size=po(this,t),l=Object.assign({},a,this._size),c=mo(e,t,l),h=bo(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=r.width,this.height=r.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),s=this.opacity;if(!s)return;this._updateAnimationTarget(e);let n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;let a=q(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),as(t,e.textDirection),o.y+=a.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),rs(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let s=this._active,n=t.map(({datasetIndex:r,index:l})=>{let c=this.chart.getDatasetMeta(r);if(!c)throw new Error("Cannot find a dataset at index "+r);return{datasetIndex:r,element:c.data[l],index:l}}),o=!ke(s,n),a=this._positionChanged(n,e);(o||a)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let n=this.options,o=this._active||[],a=this._getActiveElements(t,o,e,s),r=this._positionChanged(a,t),l=e||!ke(a,o)||r;return l&&(this._active=a,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){let o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(r=>this.chart.data.datasets[r.datasetIndex]&&this.chart.getDatasetMeta(r.datasetIndex).controller.getParsed(r.index)!==void 0);let a=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&a.reverse(),a}_positionChanged(t,e){let{caretX:s,caretY:n,options:o}=this,a=Ee[o.position].call(this,t,e);return a!==!1&&(s!==a.x||n!==a.y)}};M(Ve,"positioners",Ee);var ah={id:"tooltip",_element:Ve,positioners:Ee,afterInit(i,t,e){e&&(i.tooltip=new Ve({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){let t=i.tooltip;if(t&&t._willRender()){let e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){let e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Ko},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},rh=Object.freeze({__proto__:null,Colors:_c,Decimation:kc,Filler:jc,Legend:qc,SubTitle:Jc,Title:Zc,Tooltip:ah}),lh=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function ch(i,t,e,s){let n=i.indexOf(t);if(n===-1)return lh(i,t,e,s);let o=i.lastIndexOf(t);return n!==o?e:n}var hh=(i,t)=>i===null?null:Y(Math.round(i),0,t);function yo(i){let t=this.getLabels();return i>=0&&i<t.length?t[i]:i}var Ie=class extends Ut{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let s=this.getLabels();for(let{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(A(t))return null;let s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:ch(s,t,D(e,t),this._addedLabels),hh(e,s.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){let t=this.min,e=this.max,s=this.options.offset,n=[],o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let a=t;a<=e;a++)n.push({value:a});return n}getLabelForValue(t){return yo.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}};M(Ie,"id","category"),M(Ie,"defaults",{ticks:{callback:yo}});function dh(i,t){let e=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=t,b=!A(a),x=!A(r),v=!A(c),y=(m-p)/(d+1),_=Vi((m-p)/g/f)*f,k,w,S,P;if(_<1e-14&&!b&&!x)return[{value:p},{value:m}];P=Math.ceil(m/_)-Math.floor(p/_),P>g&&(_=Vi(P*_/g/f)*f),A(l)||(k=Math.pow(10,l),_=Math.ceil(_*k)/k),n==="ticks"?(w=Math.floor(p/_)*_,S=Math.ceil(m/_)*_):(w=p,S=m),b&&x&&o&&an((r-a)/o,_/1e3)?(P=Math.round(Math.min((r-a)/_,h)),_=(r-a)/P,w=a,S=r):v?(w=b?a:w,S=x?r:S,P=c-1,_=(S-w)/P):(P=(S-w)/_,ee(P,Math.round(P),_/1e3)?P=Math.round(P):P=Math.ceil(P));let O=Math.max(Ni(_),Ni(w));k=Math.pow(10,A(l)?O:l),w=Math.round(w*k)/k,S=Math.round(S*k)/k;let C=0;for(b&&(u&&w!==a?(e.push({value:a}),w<a&&C++,ee(Math.round((w+C*_)*k)/k,a,vo(a,y,i))&&C++):w<a&&C++);C<P;++C){let L=Math.round((w+C*_)*k)/k;if(x&&L>r)break;e.push({value:L})}return x&&u&&S!==r?e.length&&ee(e[e.length-1].value,r,vo(r,y,i))?e[e.length-1].value=r:e.push({value:r}):(!x||S===r)&&e.push({value:S}),e}function vo(i,t,{horizontal:e,minRotation:s}){let n=ot(s),o=(e?Math.sin(n):Math.cos(n))||.001,a=.75*t*(""+i).length;return Math.min(t/o,a)}var pe=class extends Ut{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return A(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds(),{min:n,max:o}=this,a=l=>n=e?n:l,r=l=>o=s?o:l;if(t){let l=lt(n),c=lt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);r(o+l),t||a(n-l)}this.min=n,this.max=o}getTickLimit(){let t=this.options.ticks,{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,s=this.getTickLimit();s=Math.max(2,s);let n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,a=dh(n,o);return t.bounds==="ticks"&&Wi(a,this,"value"),t.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){let t=this.ticks,e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){let n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return se(t,this.chart.options.locale,this.options.ticks.format)}},Fe=class extends pe{determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=N(t)?t:0,this.max=N(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,s=ot(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}};M(Fe,"id","linear"),M(Fe,"defaults",{ticks:{callback:we.formatters.numeric}});var He=i=>Math.floor(xt(i)),Nt=(i,t)=>Math.pow(10,He(i)+t);function Mo(i){return i/Math.pow(10,He(i))===1}function ko(i,t,e){let s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function uh(i,t){let e=t-i,s=He(e);for(;ko(i,t,s)>10;)s++;for(;ko(i,t,s)<10;)s--;return Math.min(s,He(i))}function fh(i,{min:t,max:e}){t=J(i.min,t);let s=[],n=He(t),o=uh(t,e),a=o<0?Math.pow(10,Math.abs(o)):1,r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*a)/a,h=Math.floor((t-l)/r/10)*r*10,d=Math.floor((c-h)/Math.pow(10,o)),u=J(i.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;u<e;)s.push({value:u,major:Mo(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((l+h+d*Math.pow(10,o))*a)/a;let f=J(i.max,u);return s.push({value:f,major:Mo(f),significand:d}),s}var ze=class extends Ut{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let s=pe.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return N(s)&&s>0?s:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=N(t)?Math.max(0,t):null,this.max=N(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!N(this._userMin)&&(this.min=t===Nt(this.min,0)?Nt(this.min,-1):Nt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),s=this.min,n=this.max,o=r=>s=t?s:r,a=r=>n=e?n:r;s===n&&(s<=0?(o(1),a(10)):(o(Nt(s,-1)),a(Nt(n,1)))),s<=0&&o(Nt(n,-1)),n<=0&&a(Nt(s,1)),this.min=s,this.max=n}buildTicks(){let t=this.options,e={min:this._userMin,max:this._userMax},s=fh(e,this);return t.bounds==="ticks"&&Wi(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":se(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=xt(t),this._valueRange=xt(this.max)-xt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(xt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}};M(ze,"id","logarithmic"),M(ze,"defaults",{ticks:{callback:we.formatters.logarithmic,major:{enabled:!0}}});function Is(i){let t=i.ticks;if(t.display&&i.display){let e=q(t.backdropPadding);return D(t.font&&t.font.size,V.font.size)+e.height}return 0}function gh(i,t,e){return e=z(e)?e:[e],{w:pn(i,t.string,e),h:e.length*t.lineHeight}}function wo(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function ph(i){let t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,a=i.options.pointLabels,r=a.centerPointLabels?R/o:0;for(let l=0;l<o;l++){let c=a.setContext(i.getPointLabelContext(l));n[l]=c.padding;let h=i.getPointPosition(l,i.drawingArea+n[l],r),d=$(c.font),u=gh(i.ctx,d,i._pointLabels[l]);s[l]=u;let f=X(i.getIndexAngle(l)+r),g=Math.round(si(f)),p=wo(g,h.x,u.w,0,180),m=wo(g,h.y,u.h,90,270);mh(e,t,f,p,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=_h(i,s,n)}function mh(i,t,e,s,n){let o=Math.abs(Math.sin(e)),a=Math.abs(Math.cos(e)),r=0,l=0;s.start<t.l?(r=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-r)):s.end>t.r&&(r=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+r)),n.start<t.t?(l=(t.t-n.start)/a,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/a,i.b=Math.max(i.b,t.b+l))}function bh(i,t,e){let s=i.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=e,l=i.getPointPosition(t,s+n+a,o),c=Math.round(si(X(l.angle+H))),h=Mh(l.y,r.h,c),d=yh(c),u=vh(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function xh(i,t){if(!t)return!0;let{left:e,top:s,right:n,bottom:o}=i;return!(ht({x:e,y:s},t)||ht({x:e,y:o},t)||ht({x:n,y:s},t)||ht({x:n,y:o},t))}function _h(i,t,e){let s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:Is(o)/2,additionalAngle:a?R/n:0},c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];let d=bh(i,h,l);s.push(d),r==="auto"&&(d.visible=xh(d,c),d.visible&&(c=d))}return s}function yh(i){return i===0||i===180?"center":i<180?"left":"right"}function vh(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function Mh(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function kh(i,t,e){let{left:s,top:n,right:o,bottom:a}=e,{backdropColor:r}=t;if(!A(r)){let l=At(t.borderRadius),c=q(t.backdropPadding);i.fillStyle=r;let h=s-c.left,d=n-c.top,u=o-s+c.width,f=a-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),ne(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function wh(i,t){let{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){let o=i._pointLabelItems[n];if(!o.visible)continue;let a=s.setContext(i.getPointLabelContext(n));kh(e,a,o);let r=$(a.font),{x:l,y:c,textAlign:h}=o;Ct(e,i._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}function qo(i,t,e,s){let{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,B);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=i.getPointPosition(a,t),n.lineTo(o.x,o.y)}}function Sh(i,t,e,s,n){let o=i.ctx,a=t.circular,{color:r,lineWidth:l}=t;!a&&!s||!r||!l||e<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),qo(i,e,a,s),o.closePath(),o.stroke(),o.restore())}function Ph(i,t,e){return yt(i,{label:e,index:t,type:"pointLabel"})}var jt=class extends pe{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=q(Is(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=N(t)&&!isNaN(t)?t:0,this.max=N(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Is(this.options))}generateTickLabels(t){pe.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{let n=F(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){let t=this.options;t.display&&t.pointLabels.display?ph(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){let e=B/(this._pointLabels.length||1),s=this.options.startAngle||0;return X(t*e+ot(s))}getDistanceFromCenterForValue(t){if(A(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(A(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let s=e[t];return Ph(this.getContext(),t,s)}}getPointPosition(t,e,s=0){let n=this.getIndexAngle(t)-H+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let s=this.ctx;s.save(),s.beginPath(),qo(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){let t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,a=this._pointLabels.length,r,l,c;if(e.pointLabels.display&&wh(this,a),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);let u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);Sh(this,f,l,a,g)}}),s.display){for(t.save(),r=a-1;r>=0;r--){let h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),c=this.getPointPosition(r,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){let t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;let n=this.getIndexAngle(0),o,a;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((r,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;let c=s.setContext(this.getContext(l)),h=$(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,a=t.measureText(r.label).width,t.fillStyle=c.backdropColor;let d=q(c.backdropPadding);t.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}Ct(t,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}};M(jt,"id","radialLinear"),M(jt,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:we.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),M(jt,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),M(jt,"descriptors",{angleLines:{_fallback:"grid"}});var Oi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},tt=Object.keys(Oi);function So(i,t){return i-t}function Po(i,t){if(A(t))return null;let e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts,a=t;return typeof s=="function"&&(a=s(a)),N(a)||(a=typeof s=="string"?e.parse(a,s):e.parse(a)),a===null?null:(n&&(a=n==="week"&&(Bt(o)||o===!0)?e.startOf(a,"isoWeek",o):e.startOf(a,n)),+a)}function Do(i,t,e,s){let n=tt.length;for(let o=tt.indexOf(i);o<n-1;++o){let a=Oi[tt[o]],r=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((e-t)/(r*a.size))<=s)return tt[o]}return tt[n-1]}function Dh(i,t,e,s,n){for(let o=tt.length-1;o>=tt.indexOf(e);o--){let a=tt[o];if(Oi[a].common&&i._adapter.diff(n,s,a)>=t-1)return a}return tt[e?tt.indexOf(e):0]}function Oh(i){for(let t=tt.indexOf(i)+1,e=tt.length;t<e;++t)if(Oi[tt[t]].common)return tt[t]}function Oo(i,t,e){if(!e)i[t]=!0;else if(e.length){let{lo:s,hi:n}=ni(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Ch(i,t,e,s){let n=i._adapter,o=+n.startOf(t[0].value,s),a=t[t.length-1].value,r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=e[r],l>=0&&(t[l].major=!0);return t}function Co(i,t,e){let s=[],n={},o=t.length,a,r;for(a=0;a<o;++a)r=t[a],n[r]=a,s.push({value:r,major:!1});return o===0||!e?s:Ch(i,s,n,e)}var Yt=class extends Ut{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let s=t.time||(t.time={}),n=this._adapter=new Hr._date(t.adapters.date);n.init(e),Qt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Po(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,s=t.time.unit||"day",{min:n,max:o,minDefined:a,maxDefined:r}=this.getUserBounds();function l(c){!a&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!r&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!a||!r)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=N(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=N(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){let t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);let o=this.min,a=this.max,r=cn(n,o,a);return this._unit=e.unit||(s.autoSkip?Do(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Dh(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Oh(this._unit),this.initOffsets(n),t.reverse&&r.reverse(),Co(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);let a=t.length<3?.5:.25;e=Y(e,0,a),s=Y(s,0,a),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){let t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,a=o.unit||Do(o.minUnit,e,s,this._getLabelCapacity(e)),r=D(n.ticks.stepSize,1),l=a==="week"?o.isoWeekday:!1,c=Bt(l)||l===!0,h={},d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":a),t.diff(s,e,a)>1e5*r)throw new Error(e+" and "+s+" are too far apart with stepSize of "+r+" "+a);let g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,r,a),f++)Oo(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&Oo(h,u,g),Object.keys(h).sort(So).map(p=>+p)}getLabelForValue(t){let e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){let n=this.options.time.displayFormats,o=this._unit,a=e||n[o];return this._adapter.format(t,a)}_tickFormatFunction(t,e,s,n){let o=this.options,a=o.ticks.callback;if(a)return F(a,[t,e,s],this);let r=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&r[l],d=c&&r[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){let e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,s=this.ctx.measureText(t).width,n=ot(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),a=Math.sin(n),r=this._resolveTickFontOptions(0).size;return{w:s*o+r*a,h:s*a+r*o}}_getLabelCapacity(t){let e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Co(this,[t],this._majorUnit),n),a=this._getLabelSize(o),r=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return r>0?r:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;let n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],e,s;if(t.length)return t;let n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Po(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return $i(t.sort(So))}};M(Yt,"id","time"),M(Yt,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function xi(i,t,e){let s=0,n=i.length-1,o,a,r,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=ct(i,"pos",t)),{pos:o,time:r}=i[s],{pos:a,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=ct(i,"time",t)),{time:o,pos:r}=i[s],{time:a,pos:l}=i[n]);let c=a-o;return c?r+(l-r)*(t-o)/c:r}var Be=class extends Yt{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=xi(e,this.min),this._tableRange=xi(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let{min:e,max:s}=this,n=[],o=[],a,r,l,c,h;for(a=0,r=t.length;a<r;++a)c=t[a],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){let t=this.min,e=this.max,s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(xi(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return xi(this._table,s*this._tableRange+this._minPos,!0)}};M(Be,"id","timeseries"),M(Be,"defaults",Yt.defaults);var Ah=Object.freeze({__proto__:null,CategoryScale:Ie,LinearScale:Fe,LogarithmicScale:ze,RadialLinearScale:jt,TimeScale:Yt,TimeSeriesScale:Be}),Go=[Nr,uc,rh,Ah];at.register(...Go);var Fs=at;function Th({dataChecksum:i,labels:t,values:e}){return{dataChecksum:i,init(){Alpine.effect(()=>{Alpine.store("theme");let s=this.getChart();s&&s.destroy(),this.initChart()}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{Alpine.store("theme")==="system"&&this.$nextTick(()=>{let s=this.getChart();s&&s.destroy(),this.initChart()})})},initChart(){if(!(!this.$refs.canvas||!this.$refs.backgroundColorElement||!this.$refs.borderColorElement))return new Fs(this.$refs.canvas,{type:"line",data:{labels:t,datasets:[{data:e,borderWidth:2,fill:"start",tension:.5,backgroundColor:getComputedStyle(this.$refs.backgroundColorElement).color,borderColor:getComputedStyle(this.$refs.borderColorElement).color}]},options:{animation:{duration:0},elements:{point:{radius:0}},maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{x:{display:!1},y:{display:!1}},tooltips:{enabled:!1}}})},getChart(){return this.$refs.canvas?Fs.getChart(this.$refs.canvas):null}}}export{Th as default};
/*! Bundled license information:

@kurkle/color/dist/color.esm.js:
  (*!
   * @kurkle/color v0.3.4
   * https://github.com/kurkle/color#readme
   * (c) 2024 Jukka Kurkela
   * Released under the MIT License
   *)

chart.js/dist/chunks/helpers.dataset.js:
chart.js/dist/chart.js:
  (*!
   * Chart.js v4.5.0
   * https://www.chartjs.org
   * (c) 2025 Chart.js Contributors
   * Released under the MIT License
   *)
*/
