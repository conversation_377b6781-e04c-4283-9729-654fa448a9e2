(()=>{var te=Object.create,q=Object.defineProperty,re=Object.getPrototypeOf,ne=Object.prototype.hasOwnProperty,ie=Object.getOwnPropertyNames,se=Object.getOwnPropertyDescriptor,ae=t=>q(t,"__esModule",{value:!0}),oe=(t,n)=>()=>(n||(n={exports:{}},t(n.exports,n)),n.exports),le=(t,n,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let l of ie(n))!ne.call(t,l)&&l!=="default"&&q(t,l,{get:()=>n[l],enumerable:!(i=se(n,l))||i.enumerable});return t},fe=t=>le(ae(q(t!=null?te(re(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t),ue=oe((t,n)=>{(function(i,l,g){if(!i)return;for(var c={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},_={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},y={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},U={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},M,b=1;b<20;++b)c[111+b]="f"+b;for(b=0;b<=9;++b)c[b+96]=b.toString();function P(e,r,a){if(e.addEventListener){e.addEventListener(r,a,!1);return}e.attachEvent("on"+r,a)}function G(e){if(e.type=="keypress"){var r=String.fromCharCode(e.which);return e.shiftKey||(r=r.toLowerCase()),r}return c[e.which]?c[e.which]:_[e.which]?_[e.which]:String.fromCharCode(e.which).toLowerCase()}function V(e,r){return e.sort().join(",")===r.sort().join(",")}function J(e){var r=[];return e.shiftKey&&r.push("shift"),e.altKey&&r.push("alt"),e.ctrlKey&&r.push("ctrl"),e.metaKey&&r.push("meta"),r}function H(e){if(e.preventDefault){e.preventDefault();return}e.returnValue=!1}function F(e){if(e.stopPropagation){e.stopPropagation();return}e.cancelBubble=!0}function C(e){return e=="shift"||e=="ctrl"||e=="alt"||e=="meta"}function B(){if(!M){M={};for(var e in c)e>95&&e<112||c.hasOwnProperty(e)&&(M[c[e]]=e)}return M}function X(e,r,a){return a||(a=B()[e]?"keydown":"keypress"),a=="keypress"&&r.length&&(a="keydown"),a}function Y(e){return e==="+"?["+"]:(e=e.replace(/\+{2}/g,"+plus"),e.split("+"))}function T(e,r){var a,h,k,S=[];for(a=Y(e),k=0;k<a.length;++k)h=a[k],U[h]&&(h=U[h]),r&&r!="keypress"&&y[h]&&(h=y[h],S.push("shift")),C(h)&&S.push(h);return r=X(h,S,r),{key:h,modifiers:S,action:r}}function R(e,r){return e===null||e===l?!1:e===r?!0:R(e.parentNode,r)}function v(e){var r=this;if(e=e||l,!(r instanceof v))return new v(e);r.target=e,r._callbacks={},r._directMap={};var a={},h,k=!1,S=!1,D=!1;function E(s){s=s||{};var f=!1,p;for(p in a){if(s[p]){f=!0;continue}a[p]=0}f||(D=!1)}function I(s,f,p,o,d,m){var u,w,A=[],O=p.type;if(!r._callbacks[s])return[];for(O=="keyup"&&C(s)&&(f=[s]),u=0;u<r._callbacks[s].length;++u)if(w=r._callbacks[s][u],!(!o&&w.seq&&a[w.seq]!=w.level)&&O==w.action&&(O=="keypress"&&!p.metaKey&&!p.ctrlKey||V(f,w.modifiers))){var $=!o&&w.combo==d,ee=o&&w.seq==o&&w.level==m;($||ee)&&r._callbacks[s].splice(u,1),A.push(w)}return A}function L(s,f,p,o){r.stopCallback(f,f.target||f.srcElement,p,o)||s(f,p)===!1&&(H(f),F(f))}r._handleKey=function(s,f,p){var o=I(s,f,p),d,m={},u=0,w=!1;for(d=0;d<o.length;++d)o[d].seq&&(u=Math.max(u,o[d].level));for(d=0;d<o.length;++d){if(o[d].seq){if(o[d].level!=u)continue;w=!0,m[o[d].seq]=1,L(o[d].callback,p,o[d].combo,o[d].seq);continue}w||L(o[d].callback,p,o[d].combo)}var A=p.type=="keypress"&&S;p.type==D&&!C(s)&&!A&&E(m),S=w&&p.type=="keydown"};function K(s){typeof s.which!="number"&&(s.which=s.keyCode);var f=G(s);if(f){if(s.type=="keyup"&&k===f){k=!1;return}r.handleKey(f,J(s),s)}}function Q(){clearTimeout(h),h=setTimeout(E,1e3)}function Z(s,f,p,o){a[s]=0;function d(O){return function(){D=O,++a[s],Q()}}function m(O){L(p,O,s),o!=="keyup"&&(k=G(O)),setTimeout(E,10)}for(var u=0;u<f.length;++u){var w=u+1===f.length,A=w?m:d(o||T(f[u+1]).action);z(f[u],A,o,s,u)}}function z(s,f,p,o,d){r._directMap[s+":"+p]=f,s=s.replace(/\s+/g," ");var m=s.split(" "),u;if(m.length>1){Z(s,m,f,p);return}u=T(s,p),r._callbacks[u.key]=r._callbacks[u.key]||[],I(u.key,u.modifiers,{type:u.action},o,s,d),r._callbacks[u.key][o?"unshift":"push"]({callback:f,modifiers:u.modifiers,action:u.action,seq:o,level:d,combo:s})}r._bindMultiple=function(s,f,p){for(var o=0;o<s.length;++o)z(s[o],f,p)},P(e,"keypress",K),P(e,"keydown",K),P(e,"keyup",K)}v.prototype.bind=function(e,r,a){var h=this;return e=e instanceof Array?e:[e],h._bindMultiple.call(h,e,r,a),h},v.prototype.unbind=function(e,r){var a=this;return a.bind.call(a,e,function(){},r)},v.prototype.trigger=function(e,r){var a=this;return a._directMap[e+":"+r]&&a._directMap[e+":"+r]({},e),a},v.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},v.prototype.stopCallback=function(e,r){var a=this;if((" "+r.className+" ").indexOf(" mousetrap ")>-1||R(r,a.target))return!1;if("composedPath"in e&&typeof e.composedPath=="function"){var h=e.composedPath()[0];h!==e.target&&(r=h)}return r.tagName=="INPUT"||r.tagName=="SELECT"||r.tagName=="TEXTAREA"||r.isContentEditable},v.prototype.handleKey=function(){var e=this;return e._handleKey.apply(e,arguments)},v.addKeycodes=function(e){for(var r in e)e.hasOwnProperty(r)&&(c[r]=e[r]);M=null},v.init=function(){var e=v(l);for(var r in e)r.charAt(0)!=="_"&&(v[r]=(function(a){return function(){return e[a].apply(e,arguments)}})(r))},v.init(),i.Mousetrap=v,typeof n<"u"&&n.exports&&(n.exports=v),typeof define=="function"&&define.amd&&define(function(){return v})})(typeof window<"u"?window:null,typeof window<"u"?document:null)}),N=fe(ue());(function(t){if(t){var n={},i=t.prototype.stopCallback;t.prototype.stopCallback=function(l,g,c,_){var y=this;return y.paused?!0:n[c]||n[_]?!1:i.call(y,l,g,c)},t.prototype.bindGlobal=function(l,g,c){var _=this;if(_.bind(l,g,c),l instanceof Array){for(var y=0;y<l.length;y++)n[l[y]]=!0;return}n[l]=!0},t.init()}})(typeof Mousetrap<"u"?Mousetrap:void 0);var pe=t=>{t.directive("mousetrap",(n,{modifiers:i,expression:l},{evaluate:g})=>{let c=()=>l?g(l):n.click();i=i.map(_=>_.replace(/--/g," ").replace(/-/g,"+").replace(/\bslash\b/g,"/")),i.includes("global")&&(i=i.filter(_=>_!=="global"),N.default.bindGlobal(i,_=>{_.preventDefault(),c()})),N.default.bind(i,_=>{_.preventDefault(),c()})})},W=pe;var j=()=>({isOpen:window.Alpine.$persist(!0).as("isOpen"),isOpenDesktop:window.Alpine.$persist(!0).as("isOpenDesktop"),collapsedGroups:window.Alpine.$persist(null).as("collapsedGroups"),init(){this.resizeObserver=null,this.setUpResizeObserver(),document.addEventListener("livewire:navigated",()=>{this.setUpResizeObserver()})},setUpResizeObserver(){this.resizeObserver&&this.resizeObserver.disconnect();let t=window.innerWidth;this.resizeObserver=new ResizeObserver(()=>{let n=window.innerWidth,i=t>=1024,l=n<1024,g=n>=1024;i&&l?(this.isOpenDesktop=this.isOpen,this.isOpen&&this.close()):!i&&g&&(this.isOpen=this.isOpenDesktop),t=n}),this.resizeObserver.observe(document.body),window.innerWidth<1024?this.isOpen&&(this.isOpenDesktop=!0,this.close()):this.isOpenDesktop=this.isOpen},groupIsCollapsed(t){return this.collapsedGroups.includes(t)},collapseGroup(t){this.collapsedGroups.includes(t)||(this.collapsedGroups=this.collapsedGroups.concat(t))},toggleCollapsedGroup(t){this.collapsedGroups=this.collapsedGroups.includes(t)?this.collapsedGroups.filter(n=>n!==t):this.collapsedGroups.concat(t)},close(){this.isOpen=!1,window.innerWidth>=1024&&(this.isOpenDesktop=!1)},open(){this.isOpen=!0,window.innerWidth>=1024&&(this.isOpenDesktop=!0)}});document.addEventListener("alpine:init",()=>{let t=localStorage.getItem("theme")??getComputedStyle(document.documentElement).getPropertyValue("--default-theme-mode");window.Alpine.store("theme",t==="dark"||t==="system"&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.addEventListener("theme-changed",n=>{let i=n.detail;localStorage.setItem("theme",i),i==="system"&&(i=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.Alpine.store("theme",i)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",n=>{localStorage.getItem("theme")==="system"&&window.Alpine.store("theme",n.matches?"dark":"light")}),window.Alpine.effect(()=>{window.Alpine.store("theme")==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")})});var x=window.history.replaceState,ce=window.history.pushState;window.history.replaceState=function(t,n,i){t?.url instanceof URL&&(t.url=t.url.toString());let l=i||t?.url||window.location.href,g=window.location.href;if(l!==g){x.call(window.history,t,n,i);return}try{let c=window.history.state;JSON.stringify(t)!==JSON.stringify(c)&&x.call(window.history,t,n,i)}catch{x.call(window.history,t,n,i)}};window.history.pushState=function(t,n,i){t?.url instanceof URL&&(t.url=t.url.toString()),ce.call(window.history,t,n,i)};document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{let t=document.querySelector(".fi-main-sidebar .fi-sidebar-item.fi-active");if((!t||t.offsetParent===null)&&(t=document.querySelector(".fi-main-sidebar .fi-sidebar-group.fi-active")),!t||t.offsetParent===null)return;let n=document.querySelector(".fi-main-sidebar .fi-sidebar-nav");n&&n.scrollTo(0,t.offsetTop-window.innerHeight/2)},10)});window.setUpUnsavedDataChangesAlert=({body:t,livewireComponent:n,$wire:i})=>{window.addEventListener("beforeunload",l=>{window.jsMd5(JSON.stringify(i.data).replace(/\\/g,""))===i.savedDataHash||i?.__instance?.effects?.redirect||(l.preventDefault(),l.returnValue=!0)})};window.setUpSpaModeUnsavedDataChangesAlert=({body:t,resolveLivewireComponentUsing:n,$wire:i})=>{let l=()=>i?.__instance?.effects?.redirect?!1:window.jsMd5(JSON.stringify(i.data).replace(/\\/g,""))!==i.savedDataHash,g=()=>confirm(t);document.addEventListener("livewire:navigate",c=>{if(typeof n()<"u"){if(!l()||g())return;c.preventDefault()}}),window.addEventListener("beforeunload",c=>{l()&&(c.preventDefault(),c.returnValue=!0)})};window.setUpUnsavedActionChangesAlert=({resolveLivewireComponentUsing:t,$wire:n})=>{window.addEventListener("beforeunload",i=>{if(!(typeof t()>"u")&&(n.mountedActions?.length??0)&&!n?.__instance?.effects?.redirect){i.preventDefault(),i.returnValue=!0;return}})};document.addEventListener("alpine:init",()=>{window.Alpine.plugin(W),window.Alpine.store("sidebar",j())});})();
