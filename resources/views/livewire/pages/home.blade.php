<?php

use App\Models\Event;
use Livewire\Volt\Component;

new class extends Component {
    public $event;
    public $title;
    public $description;
    public $keywords = 'économie verte, développement durable, DGEF, forum, Lubumbashi, RDC, environnement';
    public $ogImage = '/img/hero-area.webp';
    public $schemaType = 'Event';
    public $schemaName;
    public $schemaDescription;
    public $schemaAdditionalData = [];

    public function mount()
    {
        $this->event = Event::getActiveEvent();

        if ($this->event) {
            $this->title = $this->event->title . ' - DGEF';
            $this->description = $this->event->description;
            $this->schemaName = $this->event->title;
            $this->schemaDescription = $this->event->description;
            $this->schemaAdditionalData = [
                'startDate' => $this->event->start_date->toISOString(),
                'endDate' => $this->event->end_date->toISOString(),
                'location' => [
                    '@type' => 'Place',
                    'name' => $this->event->location,
                    'address' => $this->event->address,
                ],
                'organizer' => [
                    '@type' => 'Organization',
                    'name' => 'DGEF',
                    'email' => $this->event->email,
                    'telephone' => $this->event->phone,
                ],
            ];
        } else {
            $this->title = 'Forum sur l\'Économie Verte - DGEF';
            $this->description = 'Rejoignez-nous pour le Forum DGEF sur l\'Économie Verte';
            $this->schemaName = 'Forum DGEF';
            $this->schemaDescription = 'Forum sur l\'Économie Verte';
        }
    }
}; ?>
<main>
    @if($event)
        <x-home-hero :event="$event" />
        <x-home.about :event="$event" />
        <x-home.video :event="$event" />
        <x-home.information-bar :event="$event" />
        <x-home.why-join :event="$event" />
        <x-home.counter-area :event="$event" />
        <x-home.schedules :event="$event" />
        <x-home.speakers :event="$event" />
        <x-home.sponsors :event="$event" />
        <livewire:pages.contact-us-form />
    @else
        <x-home.empty-state />
        <livewire:pages.contact-us-form />
    @endif
</main>
