<section id="contact-map" class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title-header text-center">
                    <h2 class="section-title wow fadeInUp" data-wow-delay="0.2s">Contactez-nous</h2>
                    <p class="wow fadeInDown" data-wow-delay="0.2s">Vous avez des questions sur le Forum DGEF ? N'hésitez pas à nous contacter pour plus d'informations.</p>
                </div>
            </div>
        </div>

        <!-- Messages de succès/erreur -->
        @if (session()->has('success'))
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="lni-check-mark-circle mr-2"></i>
                        {{ session('success') }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
            </div>
        @endif

        @if (session()->has('error'))
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="lni-cross-circle mr-2"></i>
                        {{ session('error') }}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
            </div>
        @endif

        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-12 col-xs-12">
                <div class="container-form wow fadeInLeft" data-wow-delay="0.2s">
                    <div class="form-wrapper">
                        <form wire:submit="submit">
                            <div class="row">
                                <div class="col-md-6 form-line">
                                    <div class="form-group">
                                        <input type="text"
                                               class="form-control @error('name') is-invalid @enderror"
                                               wire:model="name"
                                               placeholder="Votre nom complet"
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6 form-line">
                                    <div class="form-group">
                                        <input type="email"
                                               class="form-control @error('email') is-invalid @enderror"
                                               wire:model="email"
                                               placeholder="Votre adresse email"
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12 form-line">
                                    <div class="form-group">
                                        <input type="text"
                                               class="form-control @error('subject') is-invalid @enderror"
                                               wire:model="subject"
                                               placeholder="Sujet de votre message"
                                               required>
                                        @error('subject')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12 form-line">
                                    <div class="form-group">
                                        <input type="tel"
                                               class="form-control @error('phone') is-invalid @enderror"
                                               wire:model="phone"
                                               placeholder="Téléphone (optionnel)">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <textarea class="form-control @error('message') is-invalid @enderror"
                                                  rows="5"
                                                  wire:model="message"
                                                  placeholder="Votre message"
                                                  required></textarea>
                                        @error('message')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="form-submit">
                                        <button type="submit"
                                                class="btn btn-common"
                                                {{ $isSubmitting ? 'disabled' : '' }}>
                                            @if($isSubmitting)
                                                <i class="fa fa-spinner fa-spin" aria-hidden="true"></i> Envoi en cours...
                                            @else
                                                <i class="fa fa-paper-plane" aria-hidden="true"></i> Envoyer le Message
                                            @endif
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
