<div>
    <x-slot name="title">Inscription - {{ $event?->title ?? 'Événement' }}</x-slot>

    <!-- Hero Section -->
    <x-registration-hero :event="$event" />

    <!-- Registration Form Section -->
    <section id="registration-form" class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-title-header text-center">
                        <h2 class="section-title wow fadeInUp" data-wow-delay="0.2s">Formulaire d'Inscription</h2>
                        <p class="wow fadeInDown" data-wow-delay="0.2s">Remplissez le formulaire ci-dessous pour vous inscrire à l'événement.</p>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-12 col-xs-12">
                    <div class="container-form wow fadeInLeft" data-wow-delay="0.2s">
                        <div class="form-wrapper">
                            @if (session()->has('success'))
                                <div class="alert alert-success mb-4">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if (session()->has('error'))
                                <div class="alert alert-danger mb-4">
                                    {{ session('error') }}
                                </div>
                            @endif

                            <form wire:submit="register" role="form" method="post" data-toggle="validator">
                                <!-- Informations personnelles -->
                                <div class="row">
                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="text" class="form-control @error('first_name') is-invalid @enderror"
                                                   id="first_name" wire:model="first_name" placeholder="Prénom *" required>
                                            @error('first_name') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="text" class="form-control @error('last_name') is-invalid @enderror"
                                                   id="last_name" wire:model="last_name" placeholder="Nom *" required>
                                            @error('last_name') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                                   id="email" wire:model="email" placeholder="Email *" required>
                                            @error('email') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                                   id="phone" wire:model="phone" placeholder="Téléphone *" required>
                                            @error('phone') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="text" class="form-control @error('organization') is-invalid @enderror"
                                                   id="organization" wire:model="organization" placeholder="Organisation">
                                            @error('organization') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6 form-line">
                                        <div class="form-group">
                                            <input type="text" class="form-control @error('job_title') is-invalid @enderror"
                                                   id="job_title" wire:model="job_title" placeholder="Poste">
                                            @error('job_title') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12 form-line">
                                        <div class="form-group">
                                            <select class="form-control @error('participant_type') is-invalid @enderror"
                                                    id="participant_type" wire:model="participant_type" required>
                                                <option value="">Sélectionnez votre type de participant *</option>
                                                <option value="individual">Particulier</option>
                                                <option value="organization">Représentant d'organisation</option>
                                                <option value="student">Étudiant</option>
                                                <option value="media">Média</option>
                                                <option value="speaker">Intervenant</option>
                                            </select>
                                            @error('participant_type') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12 form-line">
                                        <div class="form-group">
                                            <textarea class="form-control @error('bio') is-invalid @enderror"
                                                      id="bio" rows="4" wire:model="bio"
                                                      placeholder="Biographie courte (décrivez brièvement votre parcours et vos intérêts)"></textarea>
                                            @error('bio') <div class="help-block with-errors">{{ $message }}</div> @enderror
                                        </div>
                                    </div>

                                    <!-- Centres d'intérêt -->
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="form-label mb-3"><strong>Centres d'intérêt</strong></label>
                                            <p class="text-muted small mb-3">Sélectionnez les sujets qui vous intéressent le plus :</p>
                                            <div class="row">
                                                @foreach($availableInterests as $interest)
                                                    <div class="col-md-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox"
                                                                   value="{{ $interest }}" id="interest_{{ $interest }}"
                                                                   wire:model="interests">
                                                            <label class="form-check-label" for="interest_{{ $interest }}">
                                                                {{ ucfirst(str_replace('_', ' ', $interest)) }}
                                                            </label>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Newsletter -->
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="newsletter_subscription"
                                                       wire:model="newsletter_subscription">
                                                <label class="form-check-label" for="newsletter_subscription">
                                                    Je souhaite recevoir la newsletter et les informations sur les futurs événements
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit button -->
                                    <div class="col-md-12">
                                        <div class="form-submit">
                                            <button type="submit" class="btn btn-common" wire:loading.attr="disabled">
                                                <span wire:loading.remove><i class="fa fa-user-plus" aria-hidden="true"></i> S'inscrire à l'événement</span>
                                                <span wire:loading>
                                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                                    Inscription en cours...
                                                </span>
                                            </button>
                                            <div id="msgSubmit" class="h3 text-center hidden"></div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
