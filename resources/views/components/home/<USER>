@php
    use Illuminate\Support\Str;

    $scheduleSection = $event?->getSection('schedules');
    $title = $scheduleSection?->title ?? 'Programme de l\'Événement';
    $description = $scheduleSection?->content ?? 'Découvrez le programme détaillé de notre forum sur l\'économie verte avec des sessions enrichissantes et des intervenants de renom.';

    // Group schedules by day
    $schedulesByDay = $event?->schedules()
        ->with('speaker')
        ->orderBy('date')
        ->orderBy('start_time')
        ->get()
        ->groupBy('day') ?? collect();

    $days = $schedulesByDay->keys();
@endphp

<section id="schedules" class="schedule section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="section-title-header text-center">
                    <h2 class="section-title wow fadeInUp" data-wow-delay="0.2s">{{ $title }}</h2>
                    <p class="wow fadeInDown" data-wow-delay="0.2s">{{ $description }}</p>
                </div>
            </div>
        </div>
        @if($schedulesByDay->isNotEmpty())
        <div class="row">
            <div class="col-12 mb-5 text-center">
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    @foreach($schedulesByDay as $day => $schedules)
                        @php
                            $daySlug = Str::slug($day);
                            $isFirst = $loop->first;
                            $dayNumber = $loop->iteration;
                            $firstSchedule = $schedules->first();
                            $dayDate = $firstSchedule ? $firstSchedule->date->format('d M Y') : '';
                        @endphp
                        <li class="nav-item">
                            <a class="nav-link {{ $isFirst ? 'active' : '' }}"
                               id="{{ $daySlug }}-tab"
                               data-toggle="tab"
                               href="#{{ $daySlug }}"
                               role="tab"
                               aria-controls="{{ $daySlug }}"
                               {{ $isFirst ? 'aria-expanded=true' : '' }}>
                                <div class="item-text">
                                    <h4>{{ $day }}</h4>
                                    <h5>{{ $dayDate }}</h5>
                                </div>
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="col-12">
                <div class="schedule-area row wow fadeInDown" data-wow-delay="0.3s">
                    <div class="schedule-tab-content col-12 clearfix">
                        <div class="tab-content" id="myTabContent">
                            @foreach($schedulesByDay as $day => $schedules)
                                @php
                                    $daySlug = Str::slug($day);
                                    $isFirst = $loop->first;
                                @endphp
                                <div class="tab-pane fade {{ $isFirst ? 'show active' : '' }}"
                                     id="{{ $daySlug }}"
                                     role="tabpanel"
                                     aria-labelledby="{{ $daySlug }}-tab">
                                    <div id="accordion{{ $loop->iteration }}">
                                        @foreach($schedules as $schedule)
                                            @php
                                                $dayIndex = $loop->parent->iteration;
                                                $scheduleIndex = $loop->iteration;
                                                $collapseId = "collapse{$dayIndex}_{$scheduleIndex}";
                                                $headingId = "heading{$dayIndex}_{$scheduleIndex}";
                                                $isFirstSchedule = $loop->first;
                                                $sessionType = $schedule->is_break ? 'Pause' : 'Session';
                                            @endphp
                                            <div class="card">
                                                <div id="{{ $headingId }}">
                                                    <div class="schedule-slot-time">
                                                        <span>{{ $schedule->formatted_time }}</span>
                                                        {{ $sessionType }}
                                                    </div>
                                                    <div class="{{ $isFirstSchedule ? '' : 'collapsed' }} card-header"
                                                         data-toggle="collapse"
                                                         data-target="#{{ $collapseId }}"
                                                         aria-expanded="{{ $isFirstSchedule ? 'true' : 'false' }}"
                                                         aria-controls="{{ $collapseId }}"
                                                         style="cursor: pointer;">
                                                        @if($schedule->speaker && $schedule->speaker->image)
                                                            <div class="images-box">
                                                                <img class="img-fluid"
                                                                     src="{{ asset('storage/' . $schedule->speaker->image) }}"
                                                                     alt="{{ $schedule->speaker->name }}">
                                                            </div>
                                                        @elseif(!$schedule->is_break)
                                                            <div class="images-box">
                                                                <img class="img-fluid"
                                                                     src="{{ asset('img/speaker/speakers-1.jpg') }}"
                                                                     alt="Intervenant">
                                                            </div>
                                                        @endif
                                                        <h4>{{ $schedule->title }}</h4>
                                                        @if($schedule->speaker)
                                                            <h5 class="name">{{ $schedule->speaker->name }}</h5>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div id="{{ $collapseId }}"
                                                     class="collapse {{ $isFirstSchedule ? 'show' : '' }}"
                                                     aria-labelledby="{{ $headingId }}"
                                                     data-parent="#accordion{{ $dayIndex }}">
                                                    <div class="card-body">
                                                        @if($schedule->description)
                                                            <p>{{ $schedule->description }}</p>
                                                        @endif
                                                        @if($schedule->location)
                                                            <div class="location">
                                                                <span>Lieu :</span> {{ $schedule->location }}
                                                            </div>
                                                        @endif
                                                        @if($schedule->speaker && $schedule->speaker->bio)
                                                            <div class="speaker-bio mt-3">
                                                                <strong>À propos de l'intervenant :</strong>
                                                                <p>{{ Str::limit($schedule->speaker->bio, 200) }}</p>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @else
        <div class="row">
            <div class="col-12 text-center">
                <div class="no-schedule-message">
                    <i class="lni-calendar" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                    <h4>Programme à venir</h4>
                    <p>Le programme détaillé de l'événement sera bientôt disponible. Restez connectés !</p>
                </div>
            </div>
        </div>
        @endif
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Debug: vérifier si les accordéons sont bien initialisés
    console.log('Accordéons trouvés:', document.querySelectorAll('[id^="accordion"]').length);

    // Forcer l'initialisation des accordéons si nécessaire
    $('[data-toggle="collapse"]').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('data-target');
        console.log('Clic sur accordéon, target:', target);
        $(target).collapse('toggle');
    });
});
</script>
