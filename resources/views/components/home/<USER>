@php
    $speakers = $event?->speakers()->orderBy('pivot_order')->limit(8)->get() ?? collect();
@endphp

<section id="team" class="section-padding text-center">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="section-title-header text-center">
            <h2 class="section-title wow fadeInUp" data-wow-delay="0.2s">Nos Intervenants</h2>
            <p class="wow fadeInDown" data-wow-delay="0.2s">Découvrez les experts qui partageront leurs connaissances et expériences lors du Forum DGEF sur l'Économie Verte.</p>
          </div>
        </div>
      </div>
      <div class="row justify-content-center">
        @foreach($speakers as $index => $speaker)
        <div class="col-lg-3 col-md-6 col-xs-12">
          <!-- Team Item Starts -->
          <div class="team-item wow fadeInUp" data-wow-delay="{{ 0.2 + ($index * 0.2) }}s">
            <div class="team-img">
              <img class="img-fluid" src="{{ asset($speaker->photo ?? 'img/team/team-01.jpg') }}" alt="{{ $speaker->name }}">
              <div class="team-overlay">
                <div class="overlay-social-icon text-center">
                  <ul class="social-icons">
                    @if($speaker->twitter)
                      <li><a href="{{ $speaker->twitter }}" target="_blank"><i class="lni-twitter-filled" aria-hidden="true"></i></a></li>
                    @endif
                    @if($speaker->linkedin)
                      <li><a href="{{ $speaker->linkedin }}" target="_blank"><i class="lni-linkedin-filled" aria-hidden="true"></i></a></li>
                    @endif
                    @if($speaker->website)
                      <li><a href="{{ $speaker->website }}" target="_blank"><i class="lni-world" aria-hidden="true"></i></a></li>
                    @endif
                  </ul>
                </div>
              </div>
            </div>
            <div class="info-text">
              <h3><a href="#">{{ $speaker->name }}</a></h3>
              <p>{{ $speaker->title }}@if($speaker->company), {{ $speaker->company }}@endif</p>
            </div>
          </div>
          <!-- Team Item Ends -->
        </div>
        @endforeach
      </div>
    </div>
  </section>