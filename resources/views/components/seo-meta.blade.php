@props([
    'title' => 'Forum DGEF - Économie Verte',
    'description' => 'Rejoignez-nous pour le Forum DGEF sur l\'Économie Verte, un événement phare rassemblant des experts, des décideurs et des innovateurs engagés dans le développement durable et la transition écologique.',
    'keywords' => 'économie verte, développement durable, DGEF, forum, Lubumbashi, RDC, environnement, transition écologique, innovation, durabilité',
    'ogImage' => '/prog-ogimage.png',
    'ogType' => 'website',
    'ogUrl' => null,
    'twitterCard' => 'summary_large_image',
    'schemaType' => 'Event',
    'schemaName' => null,
    'schemaDescription' => null,
    'schemaAdditionalData' => [],
    'canonicalUrl' => null
])

@php
    $currentUrl = $ogUrl ?? request()->url();
    $fullTitle = $title;
    $ogImageUrl = $ogImage ? (str_starts_with($ogImage, 'http') ? $ogImage : asset($ogImage)) : asset('/prog-ogimage.png');
    $canonicalUrl = $canonicalUrl ?? $currentUrl;
@endphp

<!-- Primary Meta Tags -->
<title>{{ $fullTitle }}</title>
<meta name="title" content="{{ $fullTitle }}">
<meta name="description" content="{{ $description }}">
<meta name="keywords" content="{{ $keywords }}">
<meta name="author" content="DGEF - Direction Générale de l'Économie et des Finances">
<meta name="robots" content="index, follow">
<meta name="language" content="French">
<meta name="revisit-after" content="7 days">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="{{ $ogType }}">
<meta property="og:url" content="{{ $currentUrl }}">
<meta property="og:title" content="{{ $fullTitle }}">
<meta property="og:description" content="{{ $description }}">
<meta property="og:image" content="{{ $ogImageUrl }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $fullTitle }}">
<meta property="og:site_name" content="Forum DGEF - Économie Verte">
<meta property="og:locale" content="fr_FR">

<!-- Twitter -->
<meta property="twitter:card" content="{{ $twitterCard }}">
<meta property="twitter:url" content="{{ $currentUrl }}">
<meta property="twitter:title" content="{{ $fullTitle }}">
<meta property="twitter:description" content="{{ $description }}">
<meta property="twitter:image" content="{{ $ogImageUrl }}">
<meta property="twitter:image:alt" content="{{ $fullTitle }}">

<!-- Additional Meta Tags -->
<meta name="theme-color" content="#4ce3c0">
<meta name="msapplication-TileColor" content="#4ce3c0">

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
<link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
<link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">

@if($schemaType && $schemaName)
@php
    // Build schema data array to avoid complex Blade nesting
    $schemaData = [
        '@context' => 'https://schema.org',
        '@type' => $schemaType,
        'name' => $schemaName ?? $fullTitle,
        'description' => $schemaDescription ?? $description,
        'url' => $currentUrl,
        'image' => $ogImageUrl,
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'DGEF - Direction Générale de l\'Économie et des Finances',
            'url' => url('/'),
            'logo' => [
                '@type' => 'ImageObject',
                'url' => asset('logo.png')
            ]
        ]
    ];

    // Add event-specific data if this is an Event schema
    if ($schemaType === 'Event' && !empty($schemaAdditionalData)) {
        if (isset($schemaAdditionalData['startDate'])) {
            $schemaData['startDate'] = $schemaAdditionalData['startDate'];
        }
        if (isset($schemaAdditionalData['endDate'])) {
            $schemaData['endDate'] = $schemaAdditionalData['endDate'];
        }
        if (isset($schemaAdditionalData['location'])) {
            $schemaData['location'] = $schemaAdditionalData['location'];
        }
        if (isset($schemaAdditionalData['organizer'])) {
            $schemaData['organizer'] = $schemaAdditionalData['organizer'];
        }
        if (isset($schemaAdditionalData['offers'])) {
            $schemaData['offers'] = $schemaAdditionalData['offers'];
        }
        $schemaData['eventStatus'] = 'https://schema.org/EventScheduled';
        $schemaData['eventAttendanceMode'] = 'https://schema.org/OfflineEventAttendanceMode';
    }
@endphp

<!-- JSON-LD Schema Markup -->
<script type="application/ld+json">
{!! json_encode($schemaData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) !!}
</script>

<!-- Breadcrumb Schema -->
<x-breadcrumb-schema :items="[
    ['name' => 'Accueil', 'url' => url('/')],
    ['name' => $schemaName ?? $fullTitle, 'url' => $currentUrl]
]" />
@endif
