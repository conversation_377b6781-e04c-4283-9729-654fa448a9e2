       <!-- Hero Area Start -->
       @php
           $heroSection = $event?->getSection('hero');
           $heroImage = $event?->hero_image ?? 'img/hero-area.webp';
           $title = $heroSection?->title ?? $event?->title ?? 'Forum sur l\'Économie Verte';
           $subtitle = $heroSection?->getMetadataValue('subtitle') ?? ($event ? $event->start_date->format('d-m') . ' - ' . $event->end_date->format('d M Y') . ' - ' . $event->location : '03-05 Octobre 2025 - Lubumbashi, RDC');
           $description = $heroSection?->content ?? $event?->description ?? 'Rejoignez-nous pour le Forum DGEF sur l\'Économie Verte, un événement phare rassemblant des experts, des décideurs et des innovateurs engagés dans le développement durable et la transition écologique.';
           $buttonText = $heroSection?->getMetadataValue('button_text') ?? 'S\'inscrire Maintenant';
           $buttonLink = $heroSection?->getMetadataValue('button_link') ?? '/register';
           $icon = $heroSection?->getMetadataValue('icon') ?? 'lni-leaf';
       @endphp

       <div id="hero-area" class="hero-area-bg" style="background: url({{ asset($heroImage) }}) no-repeat;">
           <div class="overlay"></div>
           <div class="container">
               <div class="row justify-content-center">
                   <div class="col-lg-9 col-sm-12">
                       <div class="contents text-center">
                           <div class="icon">
                               <i class="{{ $icon }}"></i>
                           </div>
                           <p class="banner-info">{{ $subtitle }}</p>
                           <h2 class="head-title">{{ $title }}</h2>
                           <p class="banner-desc">
                               {{ $description }}
                           </p>
                           <div class="banner-btn">
                               <a wire:navigate href="{{ $buttonLink }}" class="btn btn-common">{{ $buttonText }}</a>
                           </div>
                       </div>
                   </div>
               </div>
           </div>
       </div>

       <section id="count">
           <div class="container">
               <div class="row justify-content-center">
                   <div class="col-10">
                       <div class="count-wrapper text-center">
                           <div class="time-countdown wow fadeInUp" data-wow-delay="0.2s">
                               @php
                                   $targetDate = $event ? $event->start_date->format('Y-m-d') : '2025-10-03';
                               @endphp
                               <div x-data data-event-remaining data-target-date="{{ $targetDate }}" id="clock" class="time-count">
                                   <div class="time-entry days" ><span data-days></span> <b>:</b> Jours</div>
                                   <div class="time-entry hours" ><span data-hours></span> <b>:</b> Heurs</div>
                                   <div class="time-entry minutes" ><span data-minutes></span> <b>:</b> Minutes</div>
                                   <div class="time-entry seconds" ><span data-seconds></span> Secondes</div>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>
           </div>
       </section>
       <!-- Hero Area End -->
