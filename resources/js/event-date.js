// Set your target date here (example: 30 days from now)

export const makeRemainingTime = () => {
    const eventRemaining = document.querySelector("[data-event-remaining]");

    if (eventRemaining instanceof HTMLElement) {
        const targetDate = new Date();
        const targetDateStr = eventRemaining.dataset.targetDate;
        if (!targetDateStr || !/^\d{4}-\d{2}-\d{2}$/.test(targetDateStr)) {
            return;
        }
        targetDate.setTime(new Date(targetDateStr).getTime());
        let timer; 


        function updateCountdown() {
            const currentTime = new Date();
            const difference = targetDate - currentTime;

            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor(
                (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
            );
            const minutes = Math.floor(
                (difference % (1000 * 60 * 60)) / (1000 * 60),
            );
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            eventRemaining.querySelector("[data-days]").textContent = String(
                days,
            ).padStart(2, "0");
            eventRemaining.querySelector("[data-hours]").textContent = String(
                hours,
            ).padStart(2, "0");
            eventRemaining.querySelector("[data-minutes]").textContent = String(
                minutes,
            ).padStart(2, "0");
            eventRemaining.querySelector("[data-seconds]").textContent = String(
                seconds,
            ).padStart(2, "0");

            if (difference < 0) {
                clearInterval(timer);
                ["days", "hours", "minutes", "seconds"].forEach((id) => {
                    eventRemaining.querySelector(`[data-${id}]`).textContent =
                        "00";
                });
            }
        }

        updateCountdown();
        timer = setInterval(updateCountdown, 1000);
    }
};