<?php

namespace App\Livewire\Pages;

use App\Models\Contact;
use Livewire\Component;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Log;

class ContactUsForm extends Component
{
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|email|max:255')]
    public $email = '';

    #[Validate('required|string|max:255')]
    public $subject = '';

    #[Validate('required|string|max:2000')]
    public $message = '';

    #[Validate('nullable|string|max:20')]
    public $phone = '';

    public $isSubmitting = false;

    public function submit()
    {
        $this->isSubmitting = true;

        try {
            $this->validate();

            // Créer le contact dans la base de données
            $contact = Contact::create([
                'name' => $this->name,
                'email' => $this->email,
                'subject' => $this->subject,
                'message' => $this->message,
                'phone' => $this->phone,
                'status' => 'new',
            ]);

            // Envoyer l'email de notification (optionnel)
            $this->sendNotificationEmail($contact);

            // Message de succès
            session()->flash('success', 'Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.');

            // Réinitialiser le formulaire
            $this->reset(['name', 'email', 'subject', 'message', 'phone']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Les erreurs de validation sont automatiquement gérées par Livewire
            throw $e;
        } catch (\Exception $e) {
            Log::error('Contact form error: ' . $e->getMessage());
            session()->flash('error', 'Une erreur est survenue lors de l\'envoi de votre message. Veuillez réessayer.');
        } finally {
            $this->isSubmitting = false;
        }
    }

    private function sendNotificationEmail($contact)
    {
        try {
            // Ici vous pouvez ajouter la logique d'envoi d'email
            // Par exemple avec une notification ou un mailable

            // Mail::to('<EMAIL>')->send(new ContactFormSubmitted($contact));

            Log::info('Contact form submitted', [
                'contact_id' => $contact->id,
                'name' => $contact->name,
                'email' => $contact->email,
                'subject' => $contact->subject,
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to send contact notification email: ' . $e->getMessage());
            // Ne pas faire échouer le processus si l'email ne peut pas être envoyé
        }
    }

    public function render()
    {
        return view('livewire.pages.contact-us-form');
    }
}
