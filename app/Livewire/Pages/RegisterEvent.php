<?php

namespace App\Livewire\Pages;

use App\Models\Event;
use App\Models\Registration;
use Livewire\Component;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Log;

class RegisterEvent extends Component
{
    #[Validate('required|string|max:255')]
    public $first_name = '';

    #[Validate('required|string|max:255')]
    public $last_name = '';

    #[Validate('required|email|max:255')]
    public $email = '';

    #[Validate('required|string|max:20')]
    public $phone = '';

    #[Validate('nullable|string|max:255')]
    public $organization = '';

    #[Validate('nullable|string|max:255')]
    public $job_title = '';

    #[Validate('required|in:individual,organization,student,media,speaker')]
    public $participant_type = 'individual';

    #[Validate('nullable|string|max:1000')]
    public $bio = '';

    public $interests = [];

    public $newsletter_subscription = false;

    public $event;
    public $availableInterests = [
        'green_economy',
        'sustainable_development',
        'renewable_energy',
        'climate_change',
        'environmental_policy',
        'green_finance',
        'circular_economy',
        'biodiversity',
        'sustainable_agriculture',
        'clean_technology'
    ];

    public function mount()
    {
        $this->event = Event::getActiveEvent();

        if (!$this->event) {
            abort(404, 'Aucun événement actif trouvé');
        }
    }

    public function register()
    {
        $this->validate();

        try {
            Registration::create([
                'event_id' => $this->event->id,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'email' => $this->email,
                'phone' => $this->phone,
                'organization' => $this->organization,
                'job_title' => $this->job_title,
                'participant_type' => $this->participant_type,
                'bio' => $this->bio,
                'interests' => $this->interests,
                'newsletter_subscription' => $this->newsletter_subscription,
            ]);

            session()->flash('success', 'Votre inscription a été enregistrée avec succès! Vous recevrez un email de confirmation bientôt.');

            // Reset form fields but keep event
            $this->reset([
                'first_name',
                'last_name',
                'email',
                'phone',
                'organization',
                'job_title',
                'participant_type',
                'bio',
                'interests',
                'newsletter_subscription'
            ]);

        } catch (\Exception $e) {
            session()->flash('error', 'Une erreur est survenue lors de l\'inscription. Veuillez réessayer.');
            Log::error('Registration error: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.pages.register-event');
    }
}
