<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Sponsor extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'logo',
        'website',
        'tier',
        'order',
        'is_visible',
    ];

    protected $casts = [
        'order' => 'integer',
        'is_visible' => 'boolean',
    ];

    public function events(): BelongsToMany
    {
        return $this->belongsToMany(Event::class, 'event_sponsor')
                    ->withPivot(['order', 'tier_override', 'amount', 'benefits', 'is_visible'])
                    ->withTimestamps();
    }

    public function getTierColorAttribute(): string
    {
        return match($this->tier) {
            'platinum' => '#E5E4E2',
            'gold' => '#FFD700',
            'silver' => '#C0C0C0',
            'bronze' => '#CD7F32',
            'partner' => '#6B7280',
            default => '#6B7280',
        };
    }
}
