<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Registration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'event_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'organization',
        'job_title',
        'participant_type',
        'bio',
        'interests',
        'needs_accommodation',
        'accommodation_details',
        'dietary_restrictions',
        'dietary_details',
        'status',
        'confirmed_at',
        'confirmation_token',
        'newsletter_subscription',
        'emergency_contact',
    ];

    protected $casts = [
        'interests' => 'array',
        'needs_accommodation' => 'boolean',
        'dietary_restrictions' => 'boolean',
        'confirmed_at' => 'datetime',
        'newsletter_subscription' => 'boolean',
        'emergency_contact' => 'array',
    ];

    protected static function booted()
    {
        static::creating(function ($registration) {
            if (empty($registration->confirmation_token)) {
                $registration->confirmation_token = Str::random(32);
            }
        });
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function confirm(): void
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }
}
