<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Event extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'location',
        'start_date',
        'end_date',
        'is_active',
        'hero_image',
        'video_url',
        'registration_link',
        'social_links',
        'email',
        'phone',
        'address',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'social_links' => 'array',
    ];

    protected static function booted()
    {
        static::saving(function ($event) {
            // S'assurer qu'un seul événement est actif à la fois
            if ($event->is_active) {
                static::where('id', '!=', $event->id)
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
            }
        });
    }

    public function speakers(): BelongsToMany
    {
        return $this->belongsToMany(Speaker::class, 'event_speaker')
                    ->withPivot(['order', 'is_featured', 'is_keynote', 'bio_override', 'title_override'])
                    ->withTimestamps()
                    ->orderBy('pivot_order');
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    public function sponsors(): BelongsToMany
    {
        return $this->belongsToMany(Sponsor::class, 'event_sponsor')
                    ->withPivot(['order', 'tier_override', 'amount', 'benefits', 'is_visible'])
                    ->withTimestamps()
                    ->orderBy('pivot_order');
    }

    public function sections(): HasMany
    {
        return $this->hasMany(EventSection::class);
    }

    public function registrations(): HasMany
    {
        return $this->hasMany(Registration::class);
    }

    // Helper methods
    public static function getActiveEvent(): ?Event
    {
        return static::where('is_active', true)->first();
    }

    public function getSection(string $name): ?EventSection
    {
        return $this->sections()->where('name', $name)->first();
    }

    public function getSpeakersCount(): int
    {
        return $this->speakers()->count();
    }

    public function getSeatsCount(): int
    {
        $counterSection = $this->getSection('counter-area');
        return $counterSection?->getMetadataValue('seats', 800) ?? 800;
    }

    public function getWorkshopsCount(): int
    {
        $counterSection = $this->getSection('counter-area');
        return $counterSection?->getMetadataValue('workshops', 15) ?? 15;
    }

    public function getSponsorsCount(): int
    {
        return $this->sponsors()->wherePivot('is_visible', true)->count();
    }
}
