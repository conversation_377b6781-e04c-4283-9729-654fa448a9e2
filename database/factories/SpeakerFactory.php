<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Speaker>
 */
class SpeakerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'title' => $this->faker->jobTitle(),
            'company' => $this->faker->company(),
            'bio' => $this->faker->paragraph(3),
            'photo' => 'img/team/team-' . $this->faker->numberBetween(1, 4) . '.jpg',
            'twitter' => 'https://twitter.com/' . $this->faker->userName(),
            'linkedin' => 'https://linkedin.com/in/' . $this->faker->userName(),
            'website' => $this->faker->url(),
            'order' => $this->faker->numberBetween(1, 100),
            'is_visible' => true,
        ];
    }
}
