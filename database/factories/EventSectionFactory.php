<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EventSection>
 */
class EventSectionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['hero', 'about', 'video', 'information-bar', 'why-join', 'counter-area', 'schedules', 'speakers', 'sponsors']),
            'title' => $this->faker->sentence(3),
            'content' => $this->faker->paragraph(3),
            'image' => 'img/about/img1.png',
            'video_url' => null,
            'order' => $this->faker->numberBetween(1, 10),
            'is_visible' => true,
            'metadata' => [
                'subtitle' => $this->faker->sentence(2),
                'button_text' => 'En savoir plus',
                'button_link' => '#'
            ],
        ];
    }
}
