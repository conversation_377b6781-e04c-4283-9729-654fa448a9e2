<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Registration>
 */
class RegistrationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'organization' => $this->faker->company(),
            'job_title' => $this->faker->jobTitle(),
            'participant_type' => $this->faker->randomElement(['individual', 'organization', 'student', 'media', 'speaker']),
            'bio' => $this->faker->paragraph(2),
            'interests' => $this->faker->randomElements([
                'green_economy', 'sustainable_development', 'renewable_energy',
                'climate_change', 'environmental_policy', 'green_finance',
                'circular_economy', 'biodiversity', 'sustainable_agriculture', 'clean_technology'
            ], $this->faker->numberBetween(2, 5)),
            'needs_accommodation' => $this->faker->boolean(20),
            'accommodation_details' => $this->faker->boolean(20) ? $this->faker->sentence() : null,
            'dietary_restrictions' => $this->faker->boolean(30),
            'dietary_details' => $this->faker->boolean(30) ? $this->faker->sentence() : null,
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'cancelled']),
            'confirmed_at' => $this->faker->boolean(70) ? $this->faker->dateTimeThisMonth() : null,
            'newsletter_subscription' => $this->faker->boolean(80),
            'emergency_contact' => [
                'name' => $this->faker->name(),
                'phone' => $this->faker->phoneNumber(),
                'relationship' => $this->faker->randomElement(['Époux/se', 'Parent', 'Ami(e)', 'Collègue'])
            ],
        ];
    }
}
