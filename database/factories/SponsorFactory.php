<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sponsor>
 */
class SponsorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'logo' => 'img/sponsors/logo-' . $this->faker->numberBetween(1, 9) . '.png',
            'website' => $this->faker->url(),
            'tier' => $this->faker->randomElement(['platinum', 'gold', 'silver', 'bronze', 'partner']),
            'order' => $this->faker->numberBetween(1, 100),
            'is_visible' => true,
        ];
    }
}
