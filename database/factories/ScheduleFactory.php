<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Schedule>
 */
class ScheduleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startTime = $this->faker->time('H:i:s');
        $endTime = date('H:i:s', strtotime($startTime . ' +1 hour'));

        return [
            'day' => 'Day ' . $this->faker->numberBetween(1, 3),
            'date' => $this->faker->dateTimeBetween('2025-10-03', '2025-10-05')->format('Y-m-d'),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(2),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'location' => 'Salle ' . $this->faker->numberBetween(1, 5),
            'order' => $this->faker->numberBetween(1, 50),
            'is_break' => $this->faker->boolean(20), // 20% chance of being a break
        ];
    }
}
