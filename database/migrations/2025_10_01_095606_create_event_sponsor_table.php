<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_sponsor', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('sponsor_id')->constrained()->onDelete('cascade');
            $table->integer('order')->default(0);
            $table->enum('tier_override', ['platinum', 'gold', 'silver', 'bronze', 'partner'])->nullable(); // Niveau spécifique pour cet événement
            $table->decimal('amount', 10, 2)->nullable(); // Montant du sponsoring
            $table->text('benefits')->nullable(); // Avantages spécifiques pour cet événement
            $table->boolean('is_visible')->default(true);
            $table->timestamps();

            // Index unique pour éviter les doublons
            $table->unique(['event_id', 'sponsor_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_sponsor');
    }
};
