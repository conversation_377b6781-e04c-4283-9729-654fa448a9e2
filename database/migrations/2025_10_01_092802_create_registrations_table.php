<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone');
            $table->string('organization')->nullable();
            $table->string('job_title')->nullable();
            $table->enum('participant_type', ['individual', 'organization', 'student', 'media', 'speaker']);
            $table->text('bio')->nullable();
            $table->json('interests')->nullable(); // Array of interests/topics
            $table->boolean('needs_accommodation')->default(false);
            $table->text('accommodation_details')->nullable();
            $table->boolean('dietary_restrictions')->default(false);
            $table->text('dietary_details')->nullable();
            $table->enum('status', ['pending', 'confirmed', 'cancelled'])->default('pending');
            $table->timestamp('confirmed_at')->nullable();
            $table->string('confirmation_token')->nullable();
            $table->boolean('newsletter_subscription')->default(false);
            $table->json('emergency_contact')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('registrations');
    }
};
