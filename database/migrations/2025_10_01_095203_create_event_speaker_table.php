<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_speaker', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('speaker_id')->constrained()->onDelete('cascade');
            $table->integer('order')->default(0);
            $table->boolean('is_featured')->default(false); // Speaker vedette
            $table->boolean('is_keynote')->default(false); // Conférencier principal
            $table->text('bio_override')->nullable(); // Bio spécifique pour cet événement
            $table->string('title_override')->nullable(); // Titre spécifique pour cet événement
            $table->timestamps();

            // Index unique pour éviter les doublons
            $table->unique(['event_id', 'speaker_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_speaker');
    }
};
