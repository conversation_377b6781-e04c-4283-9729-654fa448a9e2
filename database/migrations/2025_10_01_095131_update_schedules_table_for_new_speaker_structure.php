<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            // La foreign key vers speakers reste car les speakers existent toujours
            // Mais on pourrait ajouter des champs supplémentaires si nécessaire
            // Par exemple, pour gérer les speakers externes qui ne sont pas dans notre base
            $table->string('external_speaker_name')->nullable()->after('speaker_id');
            $table->string('external_speaker_title')->nullable()->after('external_speaker_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->dropColumn(['external_speaker_name', 'external_speaker_title']);
        });
    }
};
