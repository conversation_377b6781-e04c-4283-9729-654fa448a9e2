<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a test user
        \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        
        // Seed speakers and sponsors first, then events
        $this->call([
            SpeakerSeeder::class,
            SponsorSeeder::class,
            EventSeeder::class,
        ]);
    }
}
