<?php

namespace Database\Seeders;

use App\Models\Speaker;
use Illuminate\Database\Seeder;

class SpeakerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $speakers = [
            [
                'name' => 'S.E ARLETTE BAHATI',
                'title' => 'Ministre Déléguée à l\'Environnement en Charge de la Nouvelle Economie du Climat',
                'company' => null,
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 1,
                'is_visible' => true,
            ],
            [
                'name' => 'Prof. <PERSON><PERSON>',
                'title' => 'Coprésident de l\'Initiative Science pour le Bassin du Congo',
                'company' => null,
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 2,
                'is_visible' => true,
            ],
            [
                'name' => 'M. <PERSON> HANDLOEGTEN',
                'title' => 'Directeur de la GIZ en RDC',
                'company' => 'GIZ RDC',
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 3,
                'is_visible' => true,
            ],
            [
                'name' => '<PERSON><PERSON> MERODE',
                'title' => 'Directeur du Parc National des Virunga',
                'company' => 'Parc National des Virunga',
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 4,
                'is_visible' => true,
            ],
            [
                'name' => 'M. Olivier MUSHIETE',
                'title' => 'Expert en Conservation et Agroforesterie, Ancien DG Intérimaire de l\'ICCN',
                'company' => 'ICCN',
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 5,
                'is_visible' => true,
            ],
            [
                'name' => 'M. Paul EFAMBE',
                'title' => 'Expert en IA, Communications Satellites, Cartographie Géospatial',
                'company' => null,
                'bio' => null,
                'photo' => 'default-user.png',
                'order' => 6,
                'is_visible' => true,
            ],
        ];

        foreach ($speakers as $speakerData) {
            Speaker::create($speakerData);
        }
    }
}
