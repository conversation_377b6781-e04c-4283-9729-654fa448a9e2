<?php

namespace Database\Seeders;

use App\Models\Event;
use App\Models\EventSection;
use App\Models\Registration;
use App\Models\Schedule;
use App\Models\Speaker;
use App\Models\Sponsor;
use Illuminate\Database\Seeder;

class EventSeeder extends Seeder
{
    public function run(): void
    {
        $event = Event::factory()->create([
            'title' => 'Session Spéciale d’EXPOBETON 10',
            'description' => 'Accélérer la Transition Verte pour une RDC Économiquement Souveraine, Inclusive et Durable.',
            'location' => 'Centre Culturel et Artistique d’Afrique Centrale, Kinshasa',
            'start_date' => '2025-10-10 09:00:00',
            'end_date' => '2025-10-10 12:00:00',
        ]);

        $this->createEventSections($event);
        $this->createSchedules($event);
        $this->attachSpeakersToEvent($event);
        $this->attachSponsorsToEvent($event);

        Registration::factory(50)->create(['event_id' => $event->id]);
    }

    private function createEventSections(Event $event): void
    {
        $sections = [
            [
                'name' => 'hero',
                'title' => 'Session Spéciale d’EXPOBETON 10',
                'content' => 'Accélérer la Transition Verte pour une RDC Économiquement Souveraine, Inclusive et Durable.',
                'order' => 1,
                'metadata' => [
                    'subtitle' => 'Vendredi 10 octobre 2025 - 09h00 à 12h00 - Kinshasa',
                    'button_text' => 'En savoir plus',
                    'button_link' => '/#schedules',
                    'icon' => 'lni-leaf',
                ],
            ],
            [
                'name' => 'about',
                'title' => 'À propos de la Conférence',
                'content' => "La RD Congo, riche de ses ressources agricoles, énergétiques et environnementales, représente aujourd'hui un partenaire majeur pour bâtir une économie verte, inclusive et résiliente, au service de son peuple et de la planète. Pour accompagner cette dynamique, ExpoBéton, acteur incontournable d'appui aux actions du Gouvernement et L'Institut du Risk Management (iRMA) coorganisent cette session spéciale à l'occasion de la 10ᵉ édition d'ExpoBéton à Kinshasa.\n\nL'iRMA est un organisme de référence en RD Congo dédié à la promotion des meilleures pratiques en gestion des risques d'entreprise et en gestion des risques de durabilité, afin de renforcer la résilience des organisations et des communautés.\n\nL'objectif principal de la Session Spéciale est de lancer officiellement le DRC Green Economy Forum comme plateforme internationale de référence en appui à l'État congolais dans la conduite de la transition verte et énergétique, et de poser les premiers jalons de l'édition complète prévue à Lubumbashi en mai 2026.",
                'image' => 'img/about/img1.png',
                'order' => 2,
                'metadata' => [
                    'features' => [
                        'Sessions de réseautage',
                        'Ateliers pratiques',
                        'Conférences magistrales',
                        'Expositions d\'innovations',
                        'Tables rondes sectorielles'
                    ]
                ]
            ],
            [
                'name' => 'video',
                'title' => 'Axes Thématiques',
                'content' => "1. Agro-Industrie et Mécanisation Responsable\n2. Accès au Financement Rural et Inclusion Financière\n3. Filières Durables et Marchés régionaux\n4. Résilience Climatique et Pratiques Agro-Écologiques\n\nTransformation Agricole Durable, Sécurité Alimentaire et Résilience Climatique\n\n1. Énergies Renouvelables et Électrification Inclusive\n2. Infrastructures Rurales et Urbaines Durables\n3. Gestion Responsable des Ressources Naturelles\n4. Innovation et Économie Circulaire\n\nInvestissements verts et infrastructures durables pour le développement territorial.",
                'video_url' => 'video/event.webm',
                'order' => 3,
            ],
        ];

        foreach ($sections as $sectionData) {
            EventSection::create(array_merge($sectionData, ['event_id' => $event->id]));
        }
    }

    private function createSchedules(Event $event): void
    {
        $schedules = [
            [
                'title' => 'Mot d’Accueil',
                'start_time' => '09:00',
                'end_time' => '09:05',
                'day' => 'Vendredi',
                'date' => '2025-10-10',
                'order' => 1,
                'is_break' => false,
            ],
            [
                'title' => 'Discours d’Ouverture',
                'start_time' => '09:05',
                'end_time' => '09:10',
                'day' => 'Vendredi',
                'date' => '2025-10-10',
                'order' => 2,
            ],
            [
                'title' => 'Transformation Agricole Durable, Sécurité Alimentaire et Résilience Climatique',
                'start_time' => '09:10',
                'end_time' => '10:30',
                'day' => 'Vendredi',
                'date' => '2025-10-10',
                'order' => 3,
                'description' => "Format : Panel\nSous-thèmes : Agro-industrie, mécanisation responsable, inclusion financière, filières durables, résilience climatique.",
            ],
            [
                'title' => 'Investissements verts et infrastructures durables pour le développement territorial',
                'start_time' => '10:40',
                'end_time' => '12:00',
                'day' => 'Vendredi',
                'date' => '2025-10-10',
                'order' => 4,
                'description' => "Format : Table ronde\nSous-thèmes : Énergies renouvelables, infrastructures durables, économie circulaire, gestion responsable des ressources naturelles.",
            ],
        ];

        foreach ($schedules as $i => $item) {
            Schedule::create(array_merge($item, [
                'event_id' => $event->id,
                'location' => 'Centre Culturel et Artistique d’Afrique Centrale',
            ]));
        }
    }

    private function attachSpeakersToEvent(Event $event): void
    {
        $speakers = Speaker::all();

        foreach ($speakers as $index => $speaker) {
            $event->speakers()->attach($speaker->id, [
                'order' => $index + 1,
                'is_featured' => $index < 4,
            ]);
        }
    }

    private function attachSponsorsToEvent(Event $event): void
    {
        
    }
}
