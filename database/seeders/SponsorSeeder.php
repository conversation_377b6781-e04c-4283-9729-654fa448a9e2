<?php

namespace Database\Seeders;

use App\Models\Sponsor;
use Illuminate\Database\Seeder;

class SponsorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $sponsors = [
            // Platinum sponsors
            [
                'name' => 'Banque Centrale du Congo',
                'logo' => 'img/sponsors/logo-1.png',
                'website' => 'https://bcc.cd',
                'tier' => 'platinum',
                'order' => 1
            ],
            [
                'name' => 'Gécamines',
                'logo' => 'img/sponsors/logo-2.png',
                'website' => 'https://gecamines.cd',
                'tier' => 'platinum',
                'order' => 2
            ],

            // Gold sponsors
            [
                'name' => 'SNEL',
                'logo' => 'img/sponsors/logo-3.png',
                'website' => 'https://snel.cd',
                'tier' => 'gold',
                'order' => 3
            ],
            [
                'name' => 'Vodacom Congo',
                'logo' => 'img/sponsors/logo-4.png',
                'website' => 'https://vodacom.cd',
                'tier' => 'gold',
                'order' => 4
            ],
            [
                'name' => 'Orange RDC',
                'logo' => 'img/sponsors/logo-5.png',
                'website' => 'https://orange.cd',
                'tier' => 'gold',
                'order' => 5
            ],

            // Silver sponsors
            [
                'name' => 'Université de Lubumbashi',
                'logo' => 'img/sponsors/logo-6.png',
                'website' => 'https://unilu.ac.cd',
                'tier' => 'silver',
                'order' => 6
            ],
            [
                'name' => 'Chambre de Commerce du Katanga',
                'logo' => 'img/sponsors/logo-7.png',
                'website' => 'https://cck.cd',
                'tier' => 'silver',
                'order' => 7
            ],

            // Partners
            [
                'name' => 'PNUD Congo',
                'logo' => 'img/sponsors/logo-8.png',
                'website' => 'https://undp.org',
                'tier' => 'partner',
                'order' => 8
            ],
            [
                'name' => 'WWF Congo',
                'logo' => 'img/sponsors/logo-9.png',
                'website' => 'https://wwf.cd',
                'tier' => 'partner',
                'order' => 9
            ]
        ];

        foreach ($sponsors as $sponsorData) {
            Sponsor::create(array_merge($sponsorData, [
                'is_visible' => true
            ]));
        }
    }
}
